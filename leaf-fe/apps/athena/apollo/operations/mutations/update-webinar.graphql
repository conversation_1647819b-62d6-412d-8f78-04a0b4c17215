mutation UpdateWebinar(
  $id: ID!
  $title: String
  $type: String
  $summary: Map
  $startTime: DateTime
  $endTime: DateTime
  $timezone: String
  $imageCloudinaryId: String
  $allowPreWebinarComments: Boolean
  $publishedRecordingUrl: String
  $posterImageUrl: String
  $state: String
  $discoverableOnHub: Boolean
  $transcript: Map
  $transcriptSummary: Map
  $showTranscriptOnHub: Boolean
  $showTranscriptSummaryOnHub: Boolean
  $recordingNeedsLogin: Boolean
  $attendanceNeedsLogin: Boolean
) {
  updateWebinar(
    id: $id
    title: $title
    type: $type
    summary: $summary
    startTime: $startTime
    endTime: $endTime
    timezone: $timezone
    imageCloudinaryId: $imageCloudinaryId
    allowPreWebinarComments: $allowPreWebinarComments
    publishedRecordingUrl: $publishedRecordingUrl
    posterImageUrl: $posterImageUrl
    state: $state
    discoverableOnHub: $discoverableOnHub
    transcript: $transcript
    transcriptSummary: $transcriptSummary
    showTranscriptOnHub: $showTranscriptOnHub
    showTranscriptSummaryOnHub: $showTranscriptSummaryOnHub
    recordingNeedsLogin: $recordingNeedsLogin
    attendanceNeedsLogin: $attendanceNeedsLogin
  ) {
    id
    title
    type
    summary
    startTime
    endTime
    timezone
    imageCloudinaryId
    allowPreWebinarComments
    state
    publishedRecordingUrl
    posterImageUrl
    discoverableOnHub
    transcript
    transcriptSummary
    showTranscriptOnHub
    showTranscriptSummaryOnHub
    recordingNeedsLogin
    attendanceNeedsLogin
  }
}
