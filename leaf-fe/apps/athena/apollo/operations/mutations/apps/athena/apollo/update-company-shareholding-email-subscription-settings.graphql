mutation UpdateCompanyShareholdingEmailSubscriptionSettings(
  $globalUnsubscribeOnRegistryImport: Boolean!
  $unsubscribeScopesOnRegistryImport: [String!]!
) {
  updateCompanyShareholdingEmailSubscriptionSettings(
    globalUnsubscribeOnRegistryImport: $globalUnsubscribeOnRegistryImport
    unsubscribeScopesOnRegistryImport: $unsubscribeScopesOnRegistryImport
  ) {
    id
    email
    investorCentreEnabled
    name
    mobileNumber
    globalUnsubscribeOnRegistryImport
    unsubscribeScopesOnRegistryImport
    ticker {
      id
      listingKey
      marketKey
      marketListingKey
    }
  }
}
