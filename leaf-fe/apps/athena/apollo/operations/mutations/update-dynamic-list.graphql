mutation UpdateDynamicList($id: ID!, $input: DynamicListInput!) {
  updateDynamicList(id: $id, dynamicList: $input) {
    id
    lastUpdatedAt
    name
    lastUpdatedByProfileUser {
      user {
        id
        email
        firstName
        lastName
      }
    }
    lastUsedOnEmail {
      id
      campaignName
    }
    estimatedContactsSize
    filters {
      key
      value
    }
    description
  }
}
