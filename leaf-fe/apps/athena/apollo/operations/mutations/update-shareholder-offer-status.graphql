mutation UpdateShareholderOfferStatus(
  $isLive: Boolean!
  $scheduledAt: IsoNaiveDatetime
  $shareholderOfferId: ID!
) {
  updateShareholderOfferStatus(
    isLive: $isLive
    scheduledAt: $scheduledAt
    shareholderOfferId: $shareholderOfferId
  ) {
    id
    title
    type
    isLive

    scheduledAt
    companyProfile {
      id
      registry
    }
    lastEditedByUser {
      email
      firstName
      lastName
    }
    insertedAt
    updatedAt
  }
}
