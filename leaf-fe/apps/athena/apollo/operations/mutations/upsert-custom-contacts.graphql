mutation UpsertCustomContacts(
  $customContacts: [ContactInput!]!
  $audienceTags: [String!]!
  $isGlobalUnsubscribe: Boolean!
  $unsubscribeScopes: [String!]!
  $applySubscriptionToNewContactOnly: Boolean!
  $clientAnswerListSource: String
  $clientAnswerLastUsage: String
) {
  upsertCustomContacts(
    customContacts: $customContacts
    audienceTags: $audienceTags
    isGlobalUnsubscribe: $isGlobalUnsubscribe
    unsubscribeScopes: $unsubscribeScopes
    applySubscriptionToNewContactOnly: $applySubscriptionToNewContactOnly
    clientAnswerListSource: $clientAnswerListSource
    clientAnswerLastUsage: $clientAnswerLastUsage
  )
}
