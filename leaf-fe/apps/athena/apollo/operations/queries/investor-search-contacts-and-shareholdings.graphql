query InvestorSearchContactsAndShareholdings(
  $contactsAfter: String
  $contactsBefore: String
  $contactsFirst: Int
  $contactsLast: Int
  $contactsOptions: OptionsInput
  $shareholdingsAfter: String
  $shareholdingsBefore: String
  $shareholdingsFirst: Int
  $shareholdingsLast: Int
  $shareholdingsOptions: OptionsInput
) {
  contacts(
    after: $contactsAfter
    before: $contactsBefore
    first: $contactsFirst
    last: $contactsLast
    options: $contactsOptions
  ) {
    edges {
      node {
        id

        email
        firstName
        lastName
        hnwIdentifiedAt
        hnwStatus
        insertedAt
        contactSource
        shareholderStatus

        shareholdings {
          id
          accountName
        }

        investor {
          id
          username
          insertedAt
        }

        tags {
          id
          name
        }

        staticLists {
          id
          name
          backgroundColor
          textColor
        }

        suppression {
          reason
        }
      }
    }

    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $contactsOptions)
  }

  shareholdings(
    after: $shareholdingsAfter
    before: $shareholdingsBefore
    first: $shareholdingsFirst
    last: $shareholdingsLast
    options: $shareholdingsOptions
  ) {
    edges {
      node {
        accountName
        currency
        currentHoldingStartDate
        brokerNameShort
        email
        id
        initialPurchaseDate
        movementCount
        shareCount
        phoneNumber
        addressCity

        shareCountRank
        hasParticipatedInSpp
        hasParticipatedInPlacement
        hnwIdentifiedAt
        hnwBehaviour

        contact {
          id
          email
          firstName
          lastName
        }
      }
    }

    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $shareholdingsOptions)
  }
}
