query DistributionAnnouncements(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  mediaAnnouncements(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id

        header
        media {
          id
          emailDistributionMethod
          email {
            id
            isDraft
            sentAt
            scheduledAt
          }
        }
        rectype
        subtypes
        postedAt
        marketSensitive
        distributedSocial {
          id
          linkedinPostId
          twitterPostId
          linkedinPostUrl
          twitterPostUrl
        }
      }
    }
    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(options: $options)
  }
}
