query Contact($id: ID!) {
  contact(id: $id) {
    id
    insertedAt
    addressCity
    addressCountry
    addressLineOne
    addressLineTwo
    addressPostcode
    addressState
    email
    firstName
    hnwIdentifiedAt
    hnwStatus
    isNominatedShareholder
    nominatedShareholderIdentifiedAt
    lastName
    phoneNumber
    totalShareholdingRank
    shareholderStatus
    creatorName
    contactSource
    sunriceGrowerNumber
    occupation
    company
    ageRange
    emailEngagementStatus
    suppression {
      id
      insertedAt
      reason
      source
    }

    creatorUser {
      id
      firstName
      lastName
      email
    }

    investor {
      id
      email
      firstName
      lastName
      username
      hnwIdentifiedAt
      hnwStatus
      isSelfNominatedShareholder
      selfNominatedShareholderIdentifiedAt
      insertedAt
      sourceType
      sourceId
      username
      hubEngagementScore

      certificate {
        id
      }

      shareholderInformations {
        id
        country
        partialHin
        postcode
      }

      shareholderInformationsUk {
        id
        accountName
        broker
        sharesOwned
        postcode
      }
    }

    shareholderStatus
    shareholdings {
      id
      accountName
      addressCity
      addressCountry
      addressPostcode
      addressLineOne
      addressLineTwo
      addressState
      biggestMovement
      brokerNameShort
      brokerPid
      holderIdMasked
      currency
      currentHoldingStartDate
      email
      estimatedProfitLoss
      estimatedTotalPurchaseValue
      estimatedTotalSaleValue
      hasParticipatedInSpp
      hasParticipatedInPlacement
      hnwIdentifiedAt
      hnwBehaviour
      initialPurchaseDate
      movementCount
      phoneNumber
      shareCount
      shareCountRank
      updatedAt
    }

    beneficialOwnerAccounts {
      id
      accountName
      addressLineOne
      addressLineTwo
      addressCity
      addressState
      addressPostcode
      addressCountry
      depot

      latestHolding {
        id
        shares
        updatedAt
        children {
          id
        }
        report {
          id
          reportDate
        }
        parent {
          id
          beneficialOwnerAccount {
            accountName
            contact {
              id
            }
          }
        }

        shareholding {
          id
          accountName
        }

        beneficialOwnerAccount {
          id
          accountName
          contact {
            id
          }
        }
      }
    }

    commsUnsubscribes {
      id
      scope
    }

    globalUnsubscribe {
      id
    }

    tags {
      id
      name
    }

    staticLists {
      id
      name
      textColor
      backgroundColor
    }
  }
}
