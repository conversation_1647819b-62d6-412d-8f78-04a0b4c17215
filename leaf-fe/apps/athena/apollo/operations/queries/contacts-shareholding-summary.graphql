query ContactShareholdingSummary(
  $contactId: ID!
  $endDate: Date!
  $startDate: Date!
  $sortOrder: SortOrder = ASC
) {
  contactShareholdingSummary(
    contactId: $contactId
    endDate: $endDate
    startDate: $startDate
    sortOrder: $sortOrder
  ) {
    id
    announcements {
      id
      header
      postedAt
    }
    dailyHoldings {
      id
      date
      balance
    }
    shareMovements {
      id
      accountName
      closingBalance
      movement
      movementType
      openingBalance
      settledAt
      estimatedPrice
    }
    timeseries {
      id
      close
      open
      high
      low
      date
      volume
      currency
    }
    beneficialOwnerHoldings {
      id
      date
      balance
    }
    beneficialOwnerHoldingsByAccount {
      id
      date
      balance
      accountName
    }
  }
}
