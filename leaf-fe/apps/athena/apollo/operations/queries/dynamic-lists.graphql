query DynamicLists(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  dynamicLists(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id
        lastUpdatedAt
        name
        lastUpdatedByProfileUser {
          user {
            id
            email
            firstName
            lastName
          }
        }
        lastUsedOnEmail {
          id
          campaignName
        }
        estimatedContactsSize
        estimatedContactableContactsSize
        filters {
          key
          value
        }
        description
        companyProfile {
          id
        }
        insertedAt
        updatedAt
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(options: $options)
  }
}
