query MediaComments($isAnnotation: Boolean, $mediaId: ID!) {
  mediaComments(isAnnotation: $isAnnotation, mediaId: $mediaId) {
    id
    insertedAt

    annotationMetadata {
      left
      pageIndex
      top
    }
    children {
      id
      insertedAt
      updatedAt
      annotationMetadata {
        left
        pageIndex
        top
      }
      companyAuthor {
        id
        email
        firstName
        lastName
      }
      content
      investorUser {
        id
        contact {
          id
          shareholderStatus
        }
        email
        firstName
        isHoldingVerified
        isSelfNominatedShareholder
        lastName
        username
      }
      likes
      private
      useCompanyAsUsername
    }
    companyAuthor {
      id
      email
      firstName
      lastName
    }
    content
    investorUser {
      id
      contact {
        id
      }
      email
      firstName
      isHoldingVerified
      isSelfNominatedShareholder
      lastName
      username
    }
    likes
    private
    useCompanyAsUsername
    userStarred {
      id
      starred
    }
    userRead {
      id
      read
    }
  }
}
