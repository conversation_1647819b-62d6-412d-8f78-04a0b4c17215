query GetMedia($id: ID!) {
  media(id: $id) {
    id
    title
    draftContent
    targetDate
    emailDistributionMethod
    distributionAnnouncementEnabled
    distributionUpdateEnabled
    distributionLinkedinEnabled
    distributionTwitterEnabled
    distributionEmailEnabled
    primaryDistribution
    insertedAt
    updatedAt
    pdfAiProcessingStatus
    totalImpressions
    cachedStats

    # Associations
    aiMediaConversions {
      id
      fileName
      content
      pageCount
      size
    }
    companyProfile {
      id
      name
    }
    tags {
      id
      name
    }
    comments {
      id
      content
    }
    likes {
      id
    }
    mediaAnnouncement {
      id
      insertedAt
      featuredOnHub
      header
      likes
      listingKey
      marketKey
      marketSensitive
      mediaId
      postedAt
      rectype
      socialVideoUrl
      subtypes
      summary
      summaryAi
      thumbnailIsPortrait
      thumbnailUrl
      totalQuestionCount
      totalSurveyResponses
      totalViewCount
      totalUniqueVisitors
      totalSignups
      totalSignupsLastWeek
      url
      videoUrl
      germanTranslatedVideoUrl
      germanTranslatedUrl
      germanTranslatedHeader
      germanTranslatedSummary
    }
    preparedAnnouncement {
      id
      title
      videoUrl
      socialVideoUrl
      summary
      summaryTiptap
      commentContent
      commentUseCompanyAsUsername
      isDraft
      mediaId
      germanTranslatedUrl
      germanTranslatedHeader
      germanTranslatedVideoUrl
      germanTranslatedSummary
      insertedAt
      updatedAt
      hashId
    }
    mediaUpdate {
      id
      title
      contentDraft
      contentPublished
      isDraft
      mediaId
      postedAt
      slug
      newsflowSlug
      includedTypes
      isPinned
      insertedAt
      previewSecret
      thumbnailUrl
      thumbnailAttachment {
        id
        thumbnailUrl
        title
        description
        type
        url
        orderId
      }
      postedBy {
        id
        email
        firstName
        lastName
      }
      lastUpdatedBy {
        id
        email
        firstName
        lastName
      }
      likes
      totalParentCompanyCommentCount
      totalActiveCommentCount
      totalActiveQuestionCount
      totalCommentCount
      totalQuestionCount
      totalSurveyResponses
      totalSignups
      totalUniqueVisitors
      totalViewCount
    }
    email {
      id
      fromName
      fromEmail
      campaignName
      isDraft
      scheduledAt
      sentAt
      lastUpdatedUser {
        id
        user {
          firstName
        }
      }
      fromName
      fromEmail
      subject
      emailHtml
      emailJson
      isWelcomeEmail
      media {
        id
        mediaAnnouncement {
          id
        }
        mediaUpdate {
          id
        }
      }

      sendToAllContacts
      sendToContacts {
        id

        email
        firstName
        lastName
      }
      sendToDynamicLists {
        id

        name
      }
      sendToStaticLists {
        id

        name
      }
      doNotSendToContacts {
        id

        email
        firstName
        lastName
      }
      doNotSendToDynamicLists {
        id

        name
      }
      doNotSendToStaticLists {
        id

        name
      }

      totalClick: stats(type: CLICK)
      totalDelivery: stats(type: DELIVERY)
      totalOpen: stats(type: OPEN)
      totalUnsubscribed: stats(type: UNSUBSCRIBED)
      totalComplaints: stats(type: COMPLAINT)
      totalBounced: stats(type: BOUNCE)
      total: stats(type: TOTAL)
    }
    linkedinSocialPost {
      id
      content
      contentFormatted
      socialPostId
      status
      platform
      scheduledAt
      publishedAt
      errorMessage
      analyticsData
      attachments
      publishedUrl
    }
    twitterSocialPost {
      id
      content
      contentFormatted
      socialPostId
      status
      platform
      scheduledAt
      publishedAt
      errorMessage
      analyticsData
      attachments
      publishedUrl
    }
  }
}
