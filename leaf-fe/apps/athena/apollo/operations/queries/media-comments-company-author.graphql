query MediaCommentsCompanyAuthor($mediaId: ID!) {
  mediaCommentsCompanyAuthor(mediaId: $mediaId) {
    id
    insertedAt
    updatedAt

    annotationMetadata {
      left
      pageIndex
      top
    }
    children {
      id
      insertedAt
      updatedAt
      annotationMetadata {
        left
        pageIndex
        top
      }
      companyAuthor {
        id
        email
        firstName
        lastName
      }
      content
      investorUser {
        id
        contact {
          id
          shareholderStatus
        }
        email
        firstName
        isHoldingVerified
        lastName
        username
      }
      likes
      private
      useCompanyAsUsername
    }
    companyAuthor {
      id
      email
      firstName
      lastName
    }
    content
    lastEditedByUser {
      id
      firstName
      lastName
    }
    likes
    useCompanyAsUsername
  }
}
