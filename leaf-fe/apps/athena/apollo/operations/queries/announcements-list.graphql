query AnnouncementsList(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  announcementsList(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        mediaAnnouncement {
          id

          featuredOnHub
          header
          likes
          listingKey
          marketKey
          mediaId
          postedAt
          socialVideoUrl
          summary
          summaryAi
          totalActiveQuestionCount
          totalCompanyCommentCount
          totalQuestionCount
          totalSurveyResponses
          totalViewCount
          videoUrl
          germanTranslatedVideoUrl
          germanTranslatedUrl
          germanTranslatedHeader
          germanTranslatedSummary
        }
        preparedAnnouncement {
          id

          commentContent
          commentUseCompanyAsUsername
          isDraft
          mediaId
          socialVideoUrl
          summary
          title
          videoUrl
          insertedAt
          updatedAt
        }
      }
    }
    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(options: $options)
  }
}
