query Email($id: ID!) {
  email(id: $id) {
    id
    fromName
    fromEmail
    campaignName
    isDraft
    scheduledAt
    sentAt
    lastUpdatedUser {
      id
      user {
        firstName
      }
    }
    fromName
    fromEmail
    subject
    emailHtml
    emailJson
    isWelcomeEmail
    media {
      id
      mediaAnnouncement {
        id
      }
      mediaUpdate {
        id
      }
    }

    sendToAllContacts
    sendToContacts {
      id

      email
      firstName
      lastName
    }
    sendToDynamicLists {
      id

      name
    }
    sendToStaticLists {
      id

      name
    }
    doNotSendToContacts {
      id

      email
      firstName
      lastName
    }
    doNotSendToDynamicLists {
      id

      name
    }
    doNotSendToStaticLists {
      id

      name
    }

    totalClick: stats(type: CLICK)
    totalDelivery: stats(type: DELIVERY)
    totalOpen: stats(type: OPEN)
    totalUnsubscribed: stats(type: UNSUBSCRIBED)
    totalComplaints: stats(type: COMPLAINT)
    totalBounced: stats(type: BOUNCE)
    total: stats(type: TOTAL)
  }
}
