query Contacts(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  contacts(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id
        insertedAt
        importedAt
        insertedAt
        hnwStatus
        hnwIdentifiedAt

        email
        firstName
        lastName
        phoneNumber

        commsUnsubscribes {
          id
          scope
        }

        globalUnsubscribe {
          id
        }

        investor {
          id
          email
          firstName
          lastName
          username
          insertedAt
        }

        suppression {
          id
          insertedAt
          reason
          source
        }

        shareholderStatus
        shareholdings {
          id
          accountName
        }

        beneficialOwnerAccounts {
          id
          accountName
        }

        tags {
          id
          name
        }
      }
    }

    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $options)
  }
}
