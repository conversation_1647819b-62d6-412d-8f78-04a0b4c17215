query MediaAnnouncement($id: ID!) {
  mediaAnnouncement(id: $id) {
    id
    insertedAt
    featuredOnHub
    header
    likes
    listingKey
    marketKey
    marketSensitive
    mediaId
    media {
      id
      emailDistributionMethod
      email {
        id
        sentAt
        isDraft
        scheduledAt
        previewEmail
        previewEmailSubject
        subject
        totalClick: stats(type: CLICK)
        totalDelivery: stats(type: DELIVERY)
        totalOpen: stats(type: OPEN)
        totalUnsubscribed: stats(type: UNSUBSCRIBED)
        totalComplaints: stats(type: COMPLAINT)
        totalBounced: stats(type: BOUNCE)
        total: stats(type: TOTAL)
      }
      tags {
        id
        name
      }
    }
    distributedSocial {
      id
      linkedinPostId
      linkedinPostedAt
      linkedinPostUrl
      twitterPostId
      twitterPostUrl
      twitterPostedAt
    }
    postedAt
    preparedAnnouncement {
      id
      socialVideoUrl
      videoUrl
      commentContent
      commentUseCompanyAsUsername
      summary
    }
    rectype
    socialVideoUrl
    subtypes
    summary
    summaryAi
    thumbnailIsPortrait
    thumbnailUrl
    totalQuestionCount
    totalSurveyResponses
    totalViewCount
    totalUniqueVisitors
    totalSignups
    totalSignupsLastWeek
    url
    videoUrl
    germanTranslatedVideoUrl
    germanTranslatedUrl
    germanTranslatedHeader
    germanTranslatedSummary
  }
}
