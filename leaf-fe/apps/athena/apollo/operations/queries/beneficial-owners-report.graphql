fragment ContactFields on Contact {
  id
  firstName
  lastName
  email
  addressLineOne
  addressLineTwo
  addressCity
  addressState
  addressPostcode
  addressCountry
  phoneNumber
}

fragment ShareholdingFields on Shareholding {
  id
  email
  accountName
  phoneNumber
  addressCity
  addressLineOne
  addressLineTwo
  addressCountry
  addressPostcode
  addressState
  contact {
    ...ContactFields
  }
}

fragment AccountFields on BeneficialOwnerAccount {
  id
  accountName
}

fragment ParticipantFields on BeneficialOwnerReportParticipant {
  id
  accountName
  shares
  layer
  type
  status
  addressLineOne
  addressLineTwo
  addressCity
  addressState
  addressPostcode
  addressCountry
  parentId
  depot
  shareholding {
    ...ShareholdingFields
  }
  contact {
    ...ContactFields
  }
  beneficialOwnerAccount {
    ...AccountFields
  }
  lastReportParticipant {
    id
    shares
  }
}

query BeneficialOwnersReport($reportId: ID!) {
  beneficialOwnersReport(reportId: $reportId) {
    id
    reportDate
    stage
    syncAt
    metadata {
      id
      previousReportId
      previousReportDate
      nextReportId
      companyTotalShares
    }
    contacts {
      ...ContactFields
    }
    layerOneParticipants {
      ...ParticipantFields
      children {
        ...ParticipantFields
        children {
          ...ParticipantFields
        }
      }
    }
  }
}
