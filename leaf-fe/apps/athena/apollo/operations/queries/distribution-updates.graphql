query DistributionUpdates(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  mediaUpdates(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id
        media {
          id
          emailDistributionMethod
          email {
            id
            isDraft
            scheduledAt
            sentAt
          }
        }
        postedAt
        title
        includedTypes
        distributedSocial {
          id
          linkedinPostId
          twitterPostId
          linkedinPostUrl
          twitterPostUrl
        }
      }
    }
    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(options: $options)
  }
}
