query ContactActivitiesV2(
  $contactId: ID!
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  contactActivitiesV2(
    contactId: $contactId
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        month
        categories {
          category
          activities {
            id
            metadata
            timestamp
            type
            companyUser
          }
        }
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(contactId: $contactId, options: $options)
  }
}
