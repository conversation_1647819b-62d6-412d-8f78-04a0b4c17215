query CustomEmails {
  customEmails {
    marketingEmail {
      sendFromEmail
      sendFromName
      replyToEmail
    }
    transactionalEmail {
      sendFromEmail
      sendFromName
      replyToEmail
    }
    isTransactionalEmailSameAsMarketingEmail
    customCampaignTemplate {
      lastEditedProfileUser {
        id
        user {
          lastName
          firstName
        }
        profile {
          id
        }
      }
      emailHtml
      emailJson
      templateType
      updatedAt
    }
    automatedDistributionTemplate {
      lastEditedProfileUser {
        id
        user {
          lastName
          firstName
        }
        profile {
          id
        }
      }
      emailHtml
      emailJson
      templateType
      updatedAt
    }
    manualDistributionTemplate {
      lastEditedProfileUser {
        id
        user {
          lastName
          firstName
        }
        profile {
          id
        }
      }
      emailHtml
      emailJson
      templateType
      updatedAt
    }
    newShareholderWelcomeTemplate {
      lastEditedProfileUser {
        id
        user {
          lastName
          firstName
        }
        profile {
          id
        }
      }
      emailHtml
      emailJson
      templateType
      updatedAt
    }
  }
}
