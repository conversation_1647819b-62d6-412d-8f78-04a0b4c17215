query CustomDomainInstruction {
  customDomain {
    id
    customDomain
    sesDkimConfigurations {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    sesMailFromConfigurations {
      configured
      name
      value
      priority
      type
      records {
        matching
        notMatching
      }
    }
    sesDmarcConfigurations {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    vercelConfiguration {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    vercelWwwRedirectConfiguration {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    rootDomain
    isDmarcVerified
    isDkimVerified
    isVercelDomainVerified
    isMailfromVerified
    canSendEmails
  }

  customDomainSwap {
    id
    customDomain
    sesDkimConfigurations {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    sesMailFromConfigurations {
      configured
      name
      value
      priority
      type
      records {
        matching
        notMatching
      }
    }
    sesDmarcConfigurations {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    vercelConfiguration {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    vercelWwwRedirectConfiguration {
      configured
      name
      value
      type
      records {
        matching
        notMatching
      }
    }
    rootDomain
    isDmarcVerified
    isDkimVerified
    isVercelDomainVerified
    isMailfromVerified
    canSendEmails
  }
}
