query ContactActivities(
  $contactId: ID!
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  contactActivities(
    contactId: $contactId
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id

        metadata
        timestamp
        type
        companyUser
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(contactId: $contactId, options: $options)
  }
}
