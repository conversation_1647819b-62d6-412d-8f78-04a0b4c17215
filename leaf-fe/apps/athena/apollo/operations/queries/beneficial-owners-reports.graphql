query BeneficialOwnersReports(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  beneficialOwnersReports(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id
        reportDate
        insertedAt
        type
        stage
        syncAt
        isUserUploaded
        disclosedInterestDocumentUploadedAt
        disclosedInterestDocumentFilename
        disclosedInterestDocumentUrl
      }
    }
    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(options: $options)
  }
}
