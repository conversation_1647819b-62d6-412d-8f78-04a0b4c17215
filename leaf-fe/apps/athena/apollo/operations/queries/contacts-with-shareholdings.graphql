query ContactsWithShareholdings(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  contacts(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id

        email
        firstName
        lastName

        shareholdings {
          id
          email
          accountName
          shareCountRank
        }

        investor {
          id
        }
      }
    }

    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $options)
  }
}
