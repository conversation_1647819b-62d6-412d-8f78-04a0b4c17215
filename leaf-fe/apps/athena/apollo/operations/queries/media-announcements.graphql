query MediaAnnouncements(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  mediaAnnouncements(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id

        featuredOnHub
        header
        likes
        listingKey
        marketKey
        mediaId
        postedAt
        socialVideoUrl
        summary
        totalActiveQuestionCount
        totalQuestionCount
        totalSurveyResponses
        totalViewCount
        videoUrl
        germanTranslatedVideoUrl
        germanTranslatedUrl
        germanTranslatedHeader
        germanTranslatedSummary
      }
    }
    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(options: $options)
  }
}
