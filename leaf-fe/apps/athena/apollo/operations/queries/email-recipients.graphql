query EmailRecipients(
  $emailId: ID!
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  emailRecipients(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
    emailId: $emailId
  ) {
    edges {
      node {
        id
        contact {
          id
          email
          firstName
          lastName
          importedAt
          investor {
            id
            firstName
          }
          shareholdings {
            id
            accountName
          }
          commsUnsubscribes {
            id
            scope
          }
          globalUnsubscribe {
            id
          }
        }
        sentAt
        trackingEmail {
          events {
            eventType
          }
        }
      }
    }
    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }
    total(options: $options, emailId: $emailId)
  }
}
