query CompanyProfileUsers(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  companyProfileUsers(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id
        jobTitle
        status
        user {
          id
          confirmedAt
          email
          firstName
          lastName
        }
        companiesRole {
          id
          name
        }
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $options)
  }
}
