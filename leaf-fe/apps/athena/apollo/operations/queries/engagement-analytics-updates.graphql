query EngagementAnalyticsUpdates(
  $startDate: NaiveDateTime!
  $endDate: NaiveDateTime!
) {
  updatesReleasedCount(startDate: $startDate, endDate: $endDate)
  updatesAnalyticsStats(startDate: $startDate, endDate: $endDate) {
    totalLikes
    totalLikesDifference
    totalQuestions
    totalQuestionsDifference
    totalSurveyResponses
    totalSurveyResponsesDifference
    totalViews
    totalViewsDifference
  }
  topThreeViewedUpdates(startDate: $startDate, endDate: $endDate) {
    id
    title
    includedTypes
    likes
    totalViewCount
    totalQuestionCount
    totalSurveyResponses
    distributedSocial {
      linkedinPostId
      linkedinPostedAt
      twitterPostId
      twitterPostedAt
    }
    email {
      sentAt
      subject
    }
  }
  updatesReachAndEngagement(startDate: $startDate, endDate: $endDate) {
    date
    updates {
      id
      title
      includedTypes
    }
    totalViews
    totalLikes
    totalQuestions
    totalSurveyResponses
  }
  updatesEmailDistributionStatistics(startDate: $startDate, endDate: $endDate) {
    updates {
      id
      title
      includedTypes
    }
    opens
    clicks
    ctr
    openRate
    date
    sends
  }
}
