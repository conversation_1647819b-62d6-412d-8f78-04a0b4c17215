query CampaignChannelShareholdingsByActivityType(
  $channel: String!
  $shareholderActivityType: ShareholderActivityType!
  $startDate: Date!
  $endDate: Date!
) {
  campaignChannelShareholdingsByActivityType(
    channel: $channel
    shareholderActivityType: $shareholderActivityType
    startDate: $startDate
    endDate: $endDate
  ) {
    id
    accountName
    email
    shareCount
    addressLineOne
    addressLineTwo
    addressCity
    addressState
    addressPostcode
    addressCountry
    movementCount
  }
}
