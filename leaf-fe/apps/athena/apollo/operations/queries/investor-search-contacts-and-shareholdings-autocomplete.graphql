query InvestorSearchContactsAndShareholdingsAutocomplete(
  $contactsAfter: String
  $contactsBefore: String
  $contactsFirst: Int
  $contactsLast: Int
  $contactsOptions: OptionsInput
  $shareholdingsAfter: String
  $shareholdingsBefore: String
  $shareholdingsFirst: Int
  $shareholdingsLast: Int
  $shareholdingsOptions: OptionsInput
) {
  contacts(
    after: $contactsAfter
    before: $contactsBefore
    first: $contactsFirst
    last: $contactsLast
    options: $contactsOptions
  ) {
    edges {
      node {
        id

        email
        firstName
        lastName

        shareholdings {
          id
          email
          accountName
          shareCountRank
        }

        investor {
          id
          username
        }

        tags {
          id
          name
        }
      }
    }

    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $contactsOptions)
  }

  shareholdings(
    after: $shareholdingsAfter
    before: $shareholdingsBefore
    first: $shareholdingsFirst
    last: $shareholdingsLast
    options: $shareholdingsOptions
  ) {
    edges {
      node {
        accountName
        currentHoldingStartDate
        brokerNameShort
        email
        id
        initialPurchaseDate
        movementCount
        shareCount
        phoneNumber
        addressCity

        contact {
          id
          email
          firstName
          lastName
        }
      }
    }

    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $shareholdingsOptions)
  }
}
