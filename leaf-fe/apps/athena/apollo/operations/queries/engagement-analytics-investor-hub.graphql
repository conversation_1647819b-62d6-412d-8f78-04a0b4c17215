query EngagementAnalyticsInvestorHub(
  $startDate: NaiveDateTime!
  $endDate: NaiveDateTime!
) {
  investorHubAnalytics(startDate: $startDate, endDate: $endDate) {
    signupsDifference
    totalSignups
    totalUniqueVisitors
    totalViews
    totalViewsDifference
    uniqueVisitorsDifference
  }
  investorHubMostEngagedInvestors(startDate: $startDate, endDate: $endDate) {
    investorUser {
      contact {
        id
        firstName
        lastName
        shareholderStatus
        hnwStatus
        hnwIdentifiedAt
        shareholdings {
          hasParticipatedInPlacement
          hasParticipatedInSpp
          hnwBehaviour
          hnwIdentifiedAt
          id
          shareCount
          shareCountRank
        }
      }
      #       todo add in resolvers for badges
      username
    }
    likes
    questions
    surveyResponses
  }
  investorHubSignupBreakdown(startDate: $startDate, endDate: $endDate) {
    existingShareholders
    leads
    pastShareholders
    nominatedShareholders
  }
  investorHubPagePerformance(startDate: $startDate, endDate: $endDate) {
    name
    unique
    total
  }
  investorHubEngagement(startDate: $startDate, endDate: $endDate) {
    close
    currency
    date
    signups
    totalViews
    totalUniqueVisitors
    announcements {
      announcementId
      header
    }
    updates {
      updateId
      title
    }
    campaigns {
      campaignId
      name
    }
  }
}
