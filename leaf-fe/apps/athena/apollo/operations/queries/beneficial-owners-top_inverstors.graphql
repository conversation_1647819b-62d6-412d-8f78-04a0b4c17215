query BeneficialOwnersTopInvestors($reportId: ID!, $query: String) {
  beneficialOwnersTopInvestors(reportId: $reportId, query: $query) {
    topInvestors {
      investmentManagerName
      investmentManagerCountry
      investmentManagerCity
      investmentManagerState
      holdingsChange
      beneficialOwnerHoldings
      percentangeOfHoldings
      rank
      hasPastHoldings
    }
    topSummary {
      rowCount
      percentangeOfHoldings
      holdingsChange
      beneficialOwnerHoldings
      absoluteChange
      hasPastHoldings
    }
    investorsGroupedByHolders {
      registeredHolderName
      beneficialOwnerName
      beneficialOwnerCountry
      beneficialOwnerState
      investmentManagerName
      investmentManagerCountry
      investmentManagerCity
      investmentManagerState
      holdingsChange
      beneficialOwnerHoldings
      percentangeOfHoldings
      rank
      hasPastHoldings
      # details for grouped ranking by registered holder below
      groupedRank
      groupedHoldingsSum
      groupedHoldingsChange
      groupedHoldingsPercentage
    }
    allSummary {
      rowCount
      percentangeOfHoldings
      holdingsChange
      beneficialOwnerHoldings
      absoluteChange
      investmentManager
      registeredHolder
      beneficialOwner
      hasPastHoldings
    }
  }
}
