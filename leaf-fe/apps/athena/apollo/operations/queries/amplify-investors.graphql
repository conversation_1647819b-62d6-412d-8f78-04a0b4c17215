query AmplifyInvestors(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $searchPhrase: String!
  $searchTags: [String!]
) {
  amplifyInvestors(
    after: $after
    before: $before
    first: $first
    last: $last
    searchPhrase: $searchPhrase
    searchTags: $searchTags
  ) {
    edges {
      node {
        id

        contact {
          id
          email
          firstName
          lastName

          investor {
            id
            email
            firstName
            lastName
            username
          }

          shareholdings {
            id
            accountName
            email
          }

          tags {
            id
            name
          }
        }

        shareholding {
          id
          accountName
          email
        }

        type
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(searchPhrase: $searchPhrase)
  }
}
