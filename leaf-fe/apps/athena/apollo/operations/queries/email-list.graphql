query EmailList(
  $after: String
  $before: String
  $first: Int
  $last: Int
  $options: OptionsInput
) {
  emailList(
    after: $after
    before: $before
    first: $first
    last: $last
    options: $options
  ) {
    edges {
      node {
        id
        fromName
        fromEmail
        campaignName
        isDraft
        lastUpdatedUser {
          user {
            firstName
            lastName
          }
        }
        updatedAt
        scheduledAt
        sentAt
        media {
          emailDistributionMethod
          mediaAnnouncement {
            id
          }
          mediaUpdate {
            id
          }
        }
      }
    }
    options {
      filters {
        key
        value
      }
      orders {
        key
        value
      }
    }

    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
    }

    total(options: $options)
  }
}
