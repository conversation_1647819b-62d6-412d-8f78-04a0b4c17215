query CertificateById($id: ID!) {
  certificateById(id: $id) {
    certificate {
      id
      certificateUrl
      insertedAt
      investorUser {
        id
        firstName
        lastName
        username
        email
        isHoldingVerified

        contact {
          id
        }

        notificationPreferences {
          id
          channel
          hasEoi
          isOn
          scope
        }
      }
      status
      type
      isExpired
      type
    }
    nextPendingId
    totalPending
  }
}
