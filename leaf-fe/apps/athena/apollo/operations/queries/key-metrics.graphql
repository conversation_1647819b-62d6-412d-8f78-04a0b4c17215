query contactsGraphData($startDate: Date!, $endDate: Date!) {
  getContactsGraphData(startDate: $startDate, endDate: $endDate) {
    date
    hubEmails
    totalEmails
  }
}

query engagementGraphData($startDate: Date!, $endDate: Date!) {
  getEngagementGraphData(startDate: $startDate, endDate: $endDate) {
    date
    uniqueVisitors
    engagedUniqueVisitors
    returningVisitors
  }
}

query campaignDistributionGraphData($lastNumberOfEmailCampaigns: Int!) {
  getCampaignDistributionGraphData(
    lastNumberOfEmailCampaigns: $lastNumberOfEmailCampaigns
  ) {
    date
    campaignName
    clickThroughRate
    openRate
    type
    totalSent
  }
}

query submitFeedback($message: String!) {
  submitFeedback(message: $message)
}
