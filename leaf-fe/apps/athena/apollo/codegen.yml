overwrite: true
schema: 'http://localhost.com:4001/graphql'
generates:
  apps/athena/apollo/generated.tsx:
    documents: 'apps/athena/apollo/operations/**/*.graphql'
    plugins:
      - 'typescript'
      - 'typescript-operations'
      - 'typescript-react-apollo'
      - add:
          content: "/* eslint-disable typescript-sort-keys/interface */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n"
    config:
      scalars:
        Date: string
        DateShort: string
        Decimal: string
        IsoNaiveDatetime: string
        IsoDatetime: string
        Map: unknown
        NaiveDateTime: string
        Time: string
        Upload: File
      withHooks: true
      withComponent: false
      withHOC: false
    hooks:
      afterOneFileWrite:
        - prettier --write
