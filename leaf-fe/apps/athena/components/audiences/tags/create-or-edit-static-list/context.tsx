import React, {
  useEffect,
  useState,
  Dispatch,
  FC,
  SetStateAction,
  useMemo,
} from 'react';
import analytics from '@analytics';
import { ApolloError, NetworkStatus } from '@apollo/client';
import { useRouter } from 'next/router';
import { initApollo } from '@/apollo/client';
import {
  useUpdateStaticListMutation,
  StaticListQuery,
  ContactsQuery,
  Options,
  useContactsQuery,
  CheckStaticListNameTakenDocument,
  CheckStaticListNameTakenQuery,
  CheckStaticListNameTakenQueryVariables,
  useCreateStaticListMutation,
  useAllContactIdsQuery,
  AllContactIdsQuery,
  useCreateStaticListMemberMutation,
  useSendHsCustomEventMutation,
  HsCustomEvent,
} from '@/apollo/generated';
import { ContactsFilters } from '@/components/investors/search/global/context';
import { useAlert } from '@/contexts/alert-context';
import { useSharedFilterContext } from '@/contexts/shared-filter-context';
import routes from '@/utils/routes';
import { TRACKING_EVENTS } from '@/utils/tracking-events';

interface CreateOrEditStaticListContextProps {
  addContactToStaticListLoading: boolean;
  allContactIdsData?: AllContactIdsQuery;
  allContactIdsDataLoading: boolean;
  colour: TagColour;
  contactsData?: ContactsQuery;
  contactsError: ApolloError | undefined;
  contactsFilters: ContactsFilters;
  contactsLoading: boolean;
  contactsNetworkStatus: NetworkStatus;
  contactsOptions: Options;
  createStaticListLoading: boolean;
  descriptionError: boolean;
  isAllContactsSelected: boolean;
  isEditMode: boolean;
  isNew: boolean;
  isOnlySelectedContacts: boolean;
  listDescription: string;
  listName: string;
  nameError: boolean;
  onClickAddTagToContact: (
    listName: string,
    contactId: string,
    staticListId?: string | undefined
  ) => void;
  onClickClearAllContactFilters: () => void;
  onClickCreateStaticList: (
    fromCampaignId?: string,
    source?: string
  ) => Promise<void>;
  onClickDeselectAllCurrentContacts: () => void;
  onClickSelectAllCurrentContacts: () => void;
  onClickUpdateStaticList: () => Promise<void>;
  page: number;
  rowsPerPage: number;
  search: string;
  selectedContactIds: string[];
  setColour: Dispatch<SetStateAction<TagColour>>;
  setContactsFilters: Dispatch<SetStateAction<ContactsFilters>>;
  setListDescription: (listDescription: string) => void;
  setListName: (listName: string) => void;
  setPage: (newPage: number) => void;
  setRowsPerPage: Dispatch<SetStateAction<number>>;
  setSearch: (newSearch: string) => void;
  setSelectedContactIds: Dispatch<SetStateAction<string[]>>;
  setSubmitting: Dispatch<SetStateAction<boolean>>;
  staticList?: NonNullable<StaticListQuery['staticList']>;
  submitting: boolean;
  toggleIsOnlySelectedContacts: Dispatch<SetStateAction<boolean>>;
  updateStaticListLoading: boolean;
}

type TagColour = {
  bgColour: string;
  textColour: string;
};

export const TAG_SELECT_COLOURS = {
  amber: 'bg-amber-400',
  fuchsia: 'bg-fuchsia-300',
  gray: 'bg-gray-300',
  green: 'bg-green-300',
  red: 'bg-red-400',
  sky: 'bg-sky-300',
  sunflower: 'bg-sunflower-300',
  violet: 'bg-violet-400',
};

export const TAG_COLOURS: TagColour[] = [
  { bgColour: 'bg-gray-100', textColour: 'text-gray-700' }, // Gray,
  { bgColour: 'bg-sky-25', textColour: 'text-sky-700' }, // Sky,
  { bgColour: 'bg-violet-50', textColour: 'text-violet-700' }, // Violet,
  { bgColour: 'bg-fuchsia-50', textColour: 'text-fuchsia-700' }, // Fuchsia,
  { bgColour: 'bg-green-50', textColour: 'text-green-700' }, // Green,
  { bgColour: 'bg-sunflower-50', textColour: 'text-sunflower-700' }, // Sunflower,
  { bgColour: 'bg-amber-50', textColour: 'text-amber-700' }, // Amber,
  { bgColour: 'bg-red-50', textColour: 'text-red-700' }, // Red,
];

const CreateOrEditStaticListContext =
  React.createContext<CreateOrEditStaticListContextProps | null>(null);

export const useCreateOrEditStaticListContext = () => {
  const context = React.useContext(CreateOrEditStaticListContext);
  if (!context) {
    throw new Error(
      'useCreateOrEditStaticListContext must be used within a CreateOrEditStaticListContextProvider'
    );
  }
  return context;
};

export const CreateOrEditStaticListContextProvider: FC<{
  children?: React.ReactNode;
  staticList?: NonNullable<StaticListQuery['staticList']>;
}> = ({ children, staticList }) => {
  const {
    contactsFilters,
    contactsOptions,
    onClickClearAllContactFilters,
    setContactIds,
    setContactsFilters,
  } = useSharedFilterContext();

  const apolloClient = initApollo();

  const { formatAndShowError, showAlert } = useAlert();
  const { pathname, push, query } = useRouter();
  const { edit, fromCampaignId, newTag, source } = query;
  const isEditMode = edit === 'true';
  const isNew = newTag === 'true';
  const [isOnlySelectedContacts, toggleIsOnlySelectedContacts] = useState(
    !isNew
  );

  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const search = query.search ? `${query.search}` : '';
  const [listName, setListName] = useState('');
  const [listDescription, setListDescription] = useState('');
  const [colour, setColour] = useState<TagColour>(
    staticList?.textColor && staticList?.backgroundColor
      ? {
          bgColour: staticList?.backgroundColor,
          textColour: staticList?.textColor,
        }
      : TAG_COLOURS[0]
  );
  const [selectedContactIds, setSelectedContactIds] = useState<string[]>(
    staticList?.membersContactIds ?? []
  );
  const [submitting, setSubmitting] = useState(false);

  const nameError =
    (listName.length > 0 && !listName.trim()) || listName.length > 50;
  const descriptionError = listDescription.length > 300;

  const { marketListingKey } = query;

  const [createStaticList, { loading: createStaticListLoading }] =
    useCreateStaticListMutation();

  const [updateStaticList, { loading: updateStaticListLoading }] =
    useUpdateStaticListMutation();

  const [addContactToStaticList, { loading: addContactToStaticListLoading }] =
    useCreateStaticListMemberMutation();

  useEffect(() => {
    const { description, name } = query;

    if (!!description || !!name) {
      setListDescription(`${description}`);
      setListName(`${name}`);
    } else if (staticList) {
      setListDescription(staticList.description ?? '');
      setListName(staticList.name);
    }
  }, [staticList, query]);

  useEffect(() => {
    if (!isEditMode && staticList) {
      setListName(staticList.name);
      setListDescription(staticList.description ?? '');
      setSelectedContactIds(staticList.membersContactIds ?? []);
      setColour({
        bgColour: staticList.backgroundColor ?? TAG_COLOURS[0].bgColour,
        textColour: staticList.textColor ?? TAG_COLOURS[0].textColour,
      });
    }
  }, [isEditMode, staticList]);

  const afterCursor = useMemo(() => {
    return Buffer.from(
      `arrayconnection:${rowsPerPage * (page - 1) - 1}`
    ).toString('base64');
  }, [page, rowsPerPage]);

  // TODO: Currently we always perform this call (which totals to 2 network calls per filter change)
  // Maybe we can optimize this by only fetching all contact ids when we need to
  const { data: allContactIdsData, loading: allContactIdsDataLoading } =
    useAllContactIdsQuery({
      fetchPolicy: 'no-cache',
      variables: {
        filters: contactsOptions.filters,
      },
    });

  const {
    data: audiencesContactsData,
    error: audiencesContactsError,
    loading: audiencesContactsLoading,
    networkStatus: audiencesContactsNetworkStatus,
  } = useContactsQuery({
    fetchPolicy: 'no-cache',
    skip: isOnlySelectedContacts && selectedContactIds.length === 0,
    variables: {
      after: afterCursor,
      first: rowsPerPage,
      options: contactsOptions,
    },
  });

  const [sendHsCustomEvent] = useSendHsCustomEventMutation({
    variables: { customEvent: HsCustomEvent.CrmActivityCompleted },
  });

  const isAllContactsSelected = useMemo(() => {
    if (!allContactIdsData?.allContactIds) return false;
    if (allContactIdsDataLoading) return false;
    return allContactIdsData.allContactIds.every((id) =>
      selectedContactIds.includes(id)
    );
  }, [allContactIdsData, allContactIdsDataLoading, selectedContactIds]);

  const onClickSelectAllCurrentContacts = () => {
    if (!allContactIdsData?.allContactIds) return;
    const currentContactIds = allContactIdsData?.allContactIds;

    setSelectedContactIds((prevSelected) => {
      const newSelected = new Set(prevSelected);
      currentContactIds.forEach((id) => newSelected.add(id));
      return Array.from(newSelected);
    });
  };

  const onClickDeselectAllCurrentContacts = () => {
    if (!allContactIdsData?.allContactIds) return;
    const currentContactIds = allContactIdsData?.allContactIds;
    setSelectedContactIds((prevSelected) =>
      prevSelected.filter((id) => !currentContactIds.includes(id))
    );
  };

  useEffect(() => {
    if (isEditMode && staticList?.totalMembers === 0) {
      toggleIsOnlySelectedContacts(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isOnlySelectedContacts) {
      setContactIds(selectedContactIds);
    } else {
      setContactIds([]);
    }
  }, [isOnlySelectedContacts, selectedContactIds, setContactIds]);

  const setSearch = (newSearch: string) => {
    push({ pathname, query: { ...query, search: `${newSearch}` } }, undefined, {
      shallow: true,
    });
  };

  // TODO: Probably a good idea to move this out of the context so the contact profile page isn't slowed down
  const onClickAddTagToContact = async (
    listName: string,
    contactId: string,
    staticListId?: string
  ) => {
    const randomIndex = Math.floor(Math.random() * TAG_COLOURS.length);
    const { bgColour, textColour } = TAG_COLOURS[randomIndex];

    const cleanedName = listName.trim();

    try {
      sendHsCustomEvent();

      if (staticListId) {
        const res = await addContactToStaticList({
          awaitRefetchQueries: true,
          refetchQueries: [
            'Contact',
            'StaticList',
            'StaticListMember',
            'MostRecentlyUsedStaticLists',
          ],
          variables: {
            contactId: contactId,
            staticListId: staticListId,
          },
        });

        if (res.data?.createStaticListMember.id) {
          showAlert({
            description: 'Tag added to contact',
            variant: 'success',
          });

          analytics.track(TRACKING_EVENTS.tags.addedToContact, {
            contactId: contactId,
            staticListId: staticListId,
            staticListName: cleanedName,
          });

          setSubmitting(false);
        } else {
          setSubmitting(false);
          formatAndShowError('Unable to create tag');
        }
      } else {
        const { data } = await apolloClient.query<
          CheckStaticListNameTakenQuery,
          CheckStaticListNameTakenQueryVariables
        >({
          fetchPolicy: 'network-only',
          query: CheckStaticListNameTakenDocument,
          variables: { name: cleanedName },
        });

        if (data.checkStaticListNameTaken) {
          setSubmitting(false);
          showAlert({
            description: 'Tag already exists.',
            variant: 'error',
          });
        } else {
          const res = await createStaticList({
            awaitRefetchQueries: true,
            refetchQueries: [
              'Contact',
              'StaticList',
              'StaticListMember',
              'MostRecentlyUsedStaticLists',
              'ExistingStaticLists',
            ],
            variables: {
              staticList: {
                backgroundColor: bgColour,
                contactIds: [contactId],
                name: cleanedName,
                textColor: textColour,
              },
            },
          });

          if (res.data?.createStaticList.id) {
            showAlert({
              description: 'Tag added to contact',
              variant: 'success',
            });

            analytics.track(TRACKING_EVENTS.tags.addedToContact, {
              contactId: contactId,
              staticListId: res.data.createStaticList.id,
              staticListName: res.data.createStaticList.name,
            });

            analytics.track(TRACKING_EVENTS.tags.createdFromContact, {
              contactId: contactId,
              staticListId: res.data.createStaticList.id,
              staticListName: res.data.createStaticList.name,
            });

            setSubmitting(false);
          } else {
            setSubmitting(false);
            formatAndShowError('Unable to add tag to contact');
          }
        }
      }
    } catch (error) {
      setSubmitting(false);
      formatAndShowError(error);
    }
  };

  const onClickCreateStaticList = async (
    fromCampaignId?: string,
    source?: string
  ) => {
    const cleanedName = listName.trim();
    const cleanedDescription = listDescription.trim();

    try {
      const { data } = await apolloClient.query<
        CheckStaticListNameTakenQuery,
        CheckStaticListNameTakenQueryVariables
      >({
        fetchPolicy: 'network-only',
        query: CheckStaticListNameTakenDocument,
        variables: { name: cleanedName },
      });

      if (data.checkStaticListNameTaken) {
        setSubmitting(false);
        showAlert({
          description:
            'Tag name already exists. Please choose a different name.',
          variant: 'error',
        });
      } else {
        // Name not taken, next step
        const res = await createStaticList({
          variables: {
            staticList: {
              backgroundColor: colour.bgColour,
              description: cleanedDescription,
              name: cleanedName,
              textColor: colour.textColour,
            },
          },
        });

        if (res.data?.createStaticList.id) {
          const staticList = res.data.createStaticList;

          showAlert({
            description: 'Tag created successfully',
            variant: 'success',
          });

          analytics.track(TRACKING_EVENTS.tags.createSaveClicked, {
            id: staticList.id,
            name: staticList.name,
          });

          setSubmitting(false);

          push({
            pathname: routes.engagement.audiences.staticLists.list.href(
              marketListingKey as string,
              `${staticList.id}`
            ),
            query: {
              edit: 'true',
              fromCampaignId: fromCampaignId,
              newTag: 'true',
              source: source,
            },
          });
        } else {
          setSubmitting(false);
          formatAndShowError('Unable to create tag');
        }
      }
    } catch (error) {
      setSubmitting(false);
      formatAndShowError(error);
    }
  };

  const onClickUpdateStaticList = async () => {
    const cleanedName = listName.trim();
    const cleanedDescription = listDescription.trim();

    // TODO: Can think of a way to skip this network call if the name hasn't changed or only the casing has changed
    try {
      const { data } = await apolloClient.query<
        CheckStaticListNameTakenQuery,
        CheckStaticListNameTakenQueryVariables
      >({
        fetchPolicy: 'network-only',
        query: CheckStaticListNameTakenDocument,
        variables: { name: cleanedName },
      });

      if (
        cleanedName.toLowerCase() == staticList?.name.toLowerCase() ||
        !data.checkStaticListNameTaken ||
        staticList?.name === cleanedName
      ) {
        await updateStaticList({
          refetchQueries: ['StaticList', 'StaticLists'],
          variables: {
            id: `${staticList?.id}`,
            staticList: {
              backgroundColor: colour.bgColour,
              contactIds: selectedContactIds,
              description: cleanedDescription,
              name: cleanedName,
              textColor: colour.textColour,
            },
          },
        });

        analytics.track(TRACKING_EVENTS.tags.editSaveClicked, {
          id: `${staticList?.id}`,
        });

        if (fromCampaignId && source) {
          await push({
            pathname: routes.engagement.campaigns.campaign.href(
              marketListingKey as string,
              fromCampaignId as string
            ),
            query: {
              field: source as string,
              staticListId: `${staticList?.id}`,
              staticListName: cleanedName,
            },
          });
        } else {
          await push({
            pathname: routes.engagement.audiences.staticLists.list.href(
              marketListingKey as string,
              `${staticList?.id}`
            ),
          });
        }
      } else {
        setSubmitting(false);
        showAlert({
          description:
            'Tag name already exists. Please choose a different name.',
          variant: 'error',
        });
      }
    } catch (error) {
      formatAndShowError(error);
    }
  };

  return (
    <CreateOrEditStaticListContext.Provider
      value={{
        addContactToStaticListLoading,
        allContactIdsData,
        allContactIdsDataLoading,
        colour,
        contactsData: audiencesContactsData,
        contactsError: audiencesContactsError,
        contactsFilters,
        contactsLoading: audiencesContactsLoading,
        contactsNetworkStatus: audiencesContactsNetworkStatus,
        contactsOptions,
        createStaticListLoading,
        descriptionError,
        isAllContactsSelected,
        isEditMode,
        isNew,
        isOnlySelectedContacts,
        listDescription,
        listName,
        nameError,
        onClickAddTagToContact,
        onClickClearAllContactFilters,
        onClickCreateStaticList,
        onClickDeselectAllCurrentContacts,
        onClickSelectAllCurrentContacts,
        onClickUpdateStaticList,
        page,
        rowsPerPage,
        search,
        selectedContactIds,
        setColour,
        setContactsFilters: (newFilters) => {
          // When changing contact filters, reset page to 1
          setPage(1);
          setContactsFilters(newFilters);
        },
        setListDescription,
        setListName,
        setPage,
        setRowsPerPage,
        setSearch,
        setSelectedContactIds,
        setSubmitting,
        staticList,
        submitting,
        toggleIsOnlySelectedContacts,
        updateStaticListLoading,
      }}
    >
      {children}
    </CreateOrEditStaticListContext.Provider>
  );
};
