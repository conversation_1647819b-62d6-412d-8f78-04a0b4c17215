// DS V2
import { useState } from 'react';
import { Typography } from '@ds';
import ContactsFilterBeneficialOwnerSharesCount from '@/components/investors/search/global/contacts/filters/beneficial-owner-shares-count';
import ContactsFilterBrokers from '@/components/investors/search/global/contacts/filters/brokers';
import ContactFilterBrokersDisclosed from '@/components/investors/search/global/contacts/filters/brokers-disclosed';
import ContactSourceTypes from '@/components/investors/search/global/contacts/filters/contact-source-types';
import ContactsFilterHasBeneficialOwnerAccount from '@/components/investors/search/global/contacts/filters/has-beneficial-owner-account';
import HasEmailTypes from '@/components/investors/search/global/contacts/filters/has-email-types';
import ContactsFilterHasShareholding from '@/components/investors/search/global/contacts/filters/has-shareholding';
import ContactsFilterHNWTypes from '@/components/investors/search/global/contacts/filters/hnw-types';
import ContactsFilterHoldingLength from '@/components/investors/search/global/contacts/filters/holding-length';
import ContactsFilterHoldingSize from '@/components/investors/search/global/contacts/filters/holding-size';
import ContactsFilterHoldingSizeDisclosed from '@/components/investors/search/global/contacts/filters/holding-size-disclosed';
import ContactsFilterLinkedToInvestor from '@/components/investors/search/global/contacts/filters/linked-to-investor';
import ContactsFilterLinkedToShareholder from '@/components/investors/search/global/contacts/filters/linked-to-shareholder';
import ContactsFilterLocation from '@/components/investors/search/global/contacts/filters/location';
import ContactsFilterNewShareholders from '@/components/investors/search/global/contacts/filters/new-shareholders';
import StaticListTags from '@/components/investors/search/global/contacts/filters/static-list-tags';
import ContactsFilterTags from '@/components/investors/search/global/contacts/filters/tags';
import ContactsFilterTopHolders from '@/components/investors/search/global/contacts/filters/top-holders';
import ContactsFilterTradingActivity from '@/components/investors/search/global/contacts/filters/trading-activity';
import PermissionWrapper from '@/components/layouts/permission-wrapper';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { useSharedFilterContext } from '@/contexts/shared-filter-context';
import { FLAGS } from '@/hooks/use-feature-toggles';
import { Permissions } from '@/hooks/use-permission';

interface Props {
  isStaticList?: boolean;
}

const ListContactsFilters: React.ComponentType<Props> = ({
  isStaticList = false,
}) => {
  const { currentCompanyProfileUser, isPremium, isUK } =
    useCurrentCompanyProfileUser();

  const { contactsFilters, setContactsFilters } = useSharedFilterContext();

  const [openHoldingSize, toggleOpenHoldingSize] = useState(false);
  const [openHoldingSizeDisclosed, toggleOpenHoldingSizeDisclosed] =
    useState(false);
  const [openBrokersDisclosed, toggleOpenBrokersDisclosed] = useState(false);
  const [openLocation, toggleOpenLocation] = useState(false);
  const [openTags, toggleOpenTags] = useState(false);
  const [openTradingActivity, toggleOpenTradingActivity] = useState(false);
  const [openContactSource, toggleContactSource] = useState(false);
  const [openHasEmail, toggleHasEmail] = useState(false);
  const [openHoldingTime, toggleHoldingTime] = useState(false);
  const [openBrokers, toggleBrokers] = useState(false);
  const [openHasShareholding, toggleHasShareholding] = useState(false);
  const [openBeneficialOwnerSharesCount, toggleBeneficialOwnerSharesCount] =
    useState(false);

  const isStaticListsFlagEnabled =
    currentCompanyProfileUser.profile.featuresEnabled.includes(
      FLAGS.staticLists
    );

  return (
    <div className="relative">
      <div className="absolute left-0 top-0 h-[calc(100vh-68px-58px)] w-full overflow-y-auto bg-white">
        <div className="space-y-6 p-4">
          <div>
            <Typography className="text-gray-700" variant="text-heading-sm">
              Contact traits
            </Typography>
            <Typography className="text-gray-500" variant="text-body-sm">
              Filters that are applicable to all your contacts.
            </Typography>

            <div className="mt-4 space-y-2">
              <ContactsFilterLinkedToShareholder
                filters={contactsFilters}
                setFilters={setContactsFilters}
              />
              <ContactsFilterHNWTypes
                filters={contactsFilters}
                setFilters={setContactsFilters}
              />
              <ContactsFilterLinkedToInvestor
                filters={contactsFilters}
                setFilters={setContactsFilters}
              />

              <div className="divide-y divide-gray-200 px-[5px]">
                {isStaticListsFlagEnabled ? (
                  <StaticListTags
                    filters={contactsFilters}
                    open={openTags}
                    setFilters={setContactsFilters}
                    toggleOpen={toggleOpenTags}
                  />
                ) : (
                  <ContactsFilterTags
                    lgAlwaysOpen
                    filters={contactsFilters}
                    open={openTags}
                    setFilters={setContactsFilters}
                    toggleOpen={toggleOpenTags}
                  />
                )}
                {isPremium && !isUK && (
                  <div>
                    <ContactsFilterLocation
                      filters={contactsFilters}
                      open={openLocation}
                      setFilters={setContactsFilters}
                      toggleOpen={toggleOpenLocation}
                    />
                  </div>
                )}
                <HasEmailTypes
                  filters={contactsFilters}
                  open={openHasEmail}
                  setFilters={setContactsFilters}
                  toggleOpen={toggleHasEmail}
                />
                <ContactSourceTypes
                  filters={contactsFilters}
                  isPremium={isPremium}
                  open={openContactSource}
                  setFilters={setContactsFilters}
                  toggleOpen={toggleContactSource}
                />
                <div className="h-px" />
              </div>
            </div>
          </div>

          {isPremium && (
            <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
              <div>
                <Typography className="text-gray-700" variant="text-heading-sm">
                  Shareholder traits
                </Typography>
                <Typography className="text-gray-500" variant="text-body-sm">
                  Only applicable to all shareholder contacts (past or present).
                  For contacts with multiple shareholdings linked, shareholder
                  traits are based on their combined net value.
                </Typography>

                <div className="mt-4 space-y-2">
                  <ContactsFilterNewShareholders
                    filters={contactsFilters}
                    setFilters={setContactsFilters}
                  />

                  <ContactsFilterTopHolders
                    filters={contactsFilters}
                    setFilters={setContactsFilters}
                  />

                  <div className="divide-y divide-gray-200 px-[15px]">
                    <ContactsFilterHasShareholding
                      filters={contactsFilters}
                      open={openHasShareholding}
                      setFilters={setContactsFilters}
                      toggleOpen={toggleHasShareholding}
                    />
                    <ContactsFilterTradingActivity
                      filters={contactsFilters}
                      open={openTradingActivity}
                      setFilters={setContactsFilters}
                      toggleOpen={toggleOpenTradingActivity}
                    />

                    <ContactsFilterHoldingSize
                      filters={contactsFilters}
                      open={openHoldingSize}
                      setFilters={setContactsFilters}
                      toggleOpen={toggleOpenHoldingSize}
                    />

                    <ContactsFilterHoldingLength
                      filters={contactsFilters}
                      open={openHoldingTime}
                      setFilters={setContactsFilters}
                      toggleOpen={toggleHoldingTime}
                    />

                    <ContactsFilterBrokers
                      filters={contactsFilters}
                      open={openBrokers}
                      setFilters={setContactsFilters}
                      toggleOpen={toggleBrokers}
                    />
                    <div className="h-px" />
                  </div>
                </div>
              </div>
            </PermissionWrapper>
          )}

          {isPremium && (
            <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
              <div>
                <Typography className="text-gray-700" variant="text-heading-sm">
                  Beneficial owner traits
                </Typography>
                <Typography className="text-gray-500" variant="text-body-sm">
                  Filters based on beneficial owner account data and
                  shareholdings.
                </Typography>

                <div className="mt-4 space-y-2">
                  <ContactsFilterHasBeneficialOwnerAccount
                    filters={contactsFilters}
                    setFilters={setContactsFilters}
                  />

                  <div className="divide-y divide-gray-200 px-[5px]">
                    <ContactsFilterBeneficialOwnerSharesCount
                      filters={contactsFilters}
                      open={openBeneficialOwnerSharesCount}
                      setFilters={setContactsFilters}
                      toggleOpen={toggleBeneficialOwnerSharesCount}
                    />

                    <div className="h-px" />
                  </div>
                </div>
              </div>
            </PermissionWrapper>
          )}

          {isUK && (
            <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
              <div>
                <Typography className="text-gray-700" variant="text-heading-sm">
                  Self-reported holdings
                </Typography>
                <Typography className="text-gray-500" variant="text-body-sm">
                  Based on shareholdings disclosed by your investors on your
                  hub.
                </Typography>

                <div className="mt-2 divide-y px-[5px]">
                  <ContactsFilterHoldingSizeDisclosed
                    filters={contactsFilters}
                    open={openHoldingSizeDisclosed}
                    setFilters={setContactsFilters}
                    toggleOpen={toggleOpenHoldingSizeDisclosed}
                  />

                  <ContactFilterBrokersDisclosed
                    filters={contactsFilters}
                    open={openBrokersDisclosed}
                    setFilters={setContactsFilters}
                    toggleOpen={toggleOpenBrokersDisclosed}
                  />
                </div>
              </div>
            </PermissionWrapper>
          )}
        </div>
      </div>
    </div>
  );
};

export default ListContactsFilters;
