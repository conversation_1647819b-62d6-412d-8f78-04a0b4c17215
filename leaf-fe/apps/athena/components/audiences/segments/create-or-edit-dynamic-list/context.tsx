import React, {
  useEffect,
  useState,
  Dispatch,
  FC,
  SetStateAction,
  useMemo,
} from 'react';
import analytics from '@analytics';
import { ApolloError, NetworkStatus } from '@apollo/client';
import { useRouter } from 'next/router';
import {
  useCreateDynamicListMutation,
  useUpdateDynamicListMutation,
  DynamicListQuery,
  FilterInput,
  ContactsQuery,
  Options,
  useContactsQuery,
} from '@/apollo/generated';
import {
  ContactsFilters,
  FilterContactSourceType,
  FilterHasEmailOptionsType,
  FilterLocationType,
  ShareholdingsFilterTraitType,
} from '@/components/investors/search/global/context';
import {
  FilterHasEntityType,
  HubSignUpsDaysAgo,
  FilterInvestorLeadType,
  FilterNewShareholderType,
  FilterShareholderType,
  FilterSubscriptionType,
  FilterTradingActivityType,
} from '@/components/investors/search/global/context';
import { useAlert } from '@/contexts/alert-context';
import { useSharedFilterContext } from '@/contexts/shared-filter-context';
import routes from '@/utils/routes';
import { TRACKING_EVENTS } from '@/utils/tracking-events';

interface CreateOrEditDynamicListContextProps {
  contactsData?: ContactsQuery;
  contactsError: ApolloError | undefined;
  contactsFilters: ContactsFilters;
  contactsLoading: boolean;
  contactsNetworkStatus: NetworkStatus;
  contactsOptions: Options;
  createDynamicListLoading: boolean;
  dynamicList?: NonNullable<DynamicListQuery['dynamicList']>;
  listDescription: string;
  listFilters: FilterInput[];
  listName: string;
  onClickClearAllContactFilters: () => void;
  onClickCreateDynamicList: () => Promise<void>;
  onClickUpdateDynamicList: () => Promise<void>;
  page: number;
  rowsPerPage: number;
  search: string;
  setContactsFilters: Dispatch<SetStateAction<ContactsFilters>>;
  setListDescription: (listDescription: string) => void;
  setListFilters: (listFilters: FilterInput[]) => void;
  setListName: (listName: string) => void;
  setPage: (newPage: number) => void;
  setRowsPerPage: Dispatch<SetStateAction<number>>;
  setSearch: (newSearch: string) => void;
  updateDynamicListLoading: boolean;
}

const CreateOrEditDynamicListContext =
  React.createContext<CreateOrEditDynamicListContextProps | null>(null);

export const useCreateOrEditDynamicListContext = () => {
  const context = React.useContext(CreateOrEditDynamicListContext);
  if (!context) {
    throw new Error(
      'useCreateOrEditDynamicListContext must be used within a CreateOrEditDynamicListContextProvider'
    );
  }
  return context;
};

export const CreateOrEditDynamicListContextProvider: FC<{
  children?: React.ReactNode;
  dynamicList?: NonNullable<DynamicListQuery['dynamicList']>;
}> = ({ children, dynamicList }) => {
  const {
    contactsFilters,
    contactsOptions,
    onClickClearAllContactFilters,
    setContactsFilters,
  } = useSharedFilterContext();

  const { formatAndShowError, showAlert } = useAlert();
  const { pathname, push, query } = useRouter();
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const search = query.search ? `${query.search}` : '';
  const [listName, setListName] = useState('');
  const [listDescription, setListDescription] = useState('');
  const [listFilters, setListFilters] = useState<FilterInput[]>([]);
  const [filtersInitialized, setFiltersInitialized] = useState(false);

  const { marketListingKey } = query;

  const [createDynamicList, { loading: createDynamicListLoading }] =
    useCreateDynamicListMutation();
  const [updateDynamicList, { loading: updateDynamicListLoading }] =
    useUpdateDynamicListMutation();

  const afterCursor = useMemo(() => {
    return Buffer.from(
      `arrayconnection:${rowsPerPage * (page - 1) - 1}`
    ).toString('base64');
  }, [page, rowsPerPage]);

  const {
    data: audiencesContactsData,
    error: audiencesContactsError,
    loading: audiencesContactsLoading,
    networkStatus: audiencesContactsNetworkStatus,
  } = useContactsQuery({
    fetchPolicy: 'no-cache',
    variables: {
      after: afterCursor,
      first: rowsPerPage,
      options: contactsOptions,
    },
  });

  useEffect(() => {
    // Prioritize query params over backend data
    // If query params exist, it's create mode
    const { description, name } = query;

    if (!!description || !!name) {
      setListDescription(`${description}`);
      setListName(`${name}`);
    } else if (dynamicList) {
      setListDescription(dynamicList.description ?? '');
      setListFilters(dynamicList.filters);
      setListName(dynamicList.name);
    }
  }, [dynamicList, query]);

  useEffect(() => {
    if (dynamicList && !filtersInitialized) {
      setContactsFilters((prev) => {
        return dynamicList.filters.reduce((acc, { key, value }) => {
          switch (key) {
            case 'has_beneficial_owner_account':
              return {
                ...acc,
                hasBeneficialOwnerAccount:
                  value === 'true'
                    ? 'linked-only'
                    : value === 'false'
                    ? 'unlinked-only'
                    : (value as FilterHasEntityType),
              };
            case 'has_investor_hub_user':
              return { ...acc, hasInvestor: value as FilterHasEntityType };
            case 'hnw_status':
              // We need to convert the value to uppercase because the enum values in ContactHnwStatuses apps/athena/apollo/generated.tsx are uppercase, and we match against them when rendering the filter chips and loading the filter options on load
              return { ...acc, hnwStatus: value?.toUpperCase() as string };
            case 'hub_sign_ups_days_ago':
              return {
                ...acc,
                hubSignUpsDaysAgo: value as HubSignUpsDaysAgo,
              };
            case 'lead_status':
              return { ...acc, leadStatus: value as FilterInvestorLeadType };
            case 'location':
              return {
                ...acc,
                location: (value as unknown as string)?.split(
                  ','
                ) as FilterLocationType,
              };
            case 'sources':
              return {
                ...acc,
                contactSource: (value as unknown as string)?.split(
                  ','
                ) as FilterContactSourceType,
              };
            case 'has_email':
              return { ...acc, hasEmail: value as FilterHasEmailOptionsType };
            case 'max_share_count':
              return { ...acc, maxShares: value as string };
            case 'min_share_count':
              return { ...acc, minShares: value as string };
            case 'max_share_count_disclosed':
              return { ...acc, maxSharesDisclosed: value as string };
            case 'min_share_count_disclosed':
              return { ...acc, minSharesDisclosed: value as string };
            case 'newholder_status':
              return {
                ...acc,
                newShareholderStatus: value as FilterNewShareholderType,
              };
            case 'shareholder_status':
              return {
                ...acc,
                shareholderStatus: value as FilterShareholderType,
              };
            case 'subscription_status':
              return {
                ...acc,
                subscriptionStatus: (value as unknown as string)?.split(
                  ','
                ) as FilterSubscriptionType,
              };
            case 'tags':
              return { ...acc, tags: value as unknown as string[] };
            case 'static_list_ids':
              return {
                ...acc,
                staticListIds: (value as unknown as string)?.split(','),
              };
            case 'brokers_disclosed':
              return {
                ...acc,
                brokersDisclosed: (value as unknown as string)?.split(','),
              };
            case 'trading_activity':
              return {
                ...acc,
                tradingActivity: (value as string)
                  .split(',')[0]
                  .trim()
                  .toLowerCase() as FilterTradingActivityType,
                tradingActivityDateRange: (value as string)
                  .split(',')
                  .slice(1)
                  .join(','),
              };
            case 'traits':
              return { ...acc, traits: value as ShareholdingsFilterTraitType };
            case 'beneficial_owner_shares_count':
              return { ...acc, beneficialOwnerSharesCount: value as string };
            default:
              return acc;
          }
        }, prev);
      });
      setFiltersInitialized(true); // Mark as initialized to prevent further updates
    }
  }, [dynamicList, filtersInitialized, setContactsFilters]);

  const setSearch = (newSearch: string) => {
    push({ pathname, query: { ...query, search: `${newSearch}` } }, undefined, {
      shallow: true,
    });
  };

  const onClickCreateDynamicList = async () => {
    try {
      const res = await createDynamicList({
        refetchQueries: ['DynamicList', 'DynamicLists', 'Contacts'],
        variables: {
          input: {
            description: listDescription,
            filters: listFilters,
            name: listName,
          },
        },
      });

      if (res.data?.createDynamicList.id) {
        const newDynamicList = res.data.createDynamicList;

        showAlert({
          description: 'Segment created successfully.',
          variant: 'success',
        });

        analytics.track(TRACKING_EVENTS.segment.createSaveClicked, {
          segmentId: newDynamicList.id,
        });

        // Redirect back to campaign if came from there
        // Otherwise redirect to segments page
        if (query.fromCampaignId && query.source) {
          await push({
            pathname: routes.engagement.campaigns.campaign.href(
              marketListingKey as string,
              query.fromCampaignId as string
            ),
            query: {
              dynamicListId: newDynamicList.id,
              dynamicListName: newDynamicList.name,
              field: query.source as 'send-to' | 'do-not-send-to',
            },
          });
        } else {
          await push({
            pathname: routes.engagement.audiences.dynamicLists.list.href(
              marketListingKey as string,
              newDynamicList.id
            ),
          });
        }
      } else {
        formatAndShowError('Unable to create segment.');
      }
    } catch (error) {
      formatAndShowError(error);
    }
  };

  const onClickUpdateDynamicList = async () => {
    try {
      await updateDynamicList({
        awaitRefetchQueries: true,
        refetchQueries: ['DynamicList', 'DynamicLists', 'Contacts'],
        variables: {
          id: `${dynamicList?.id}`,
          input: {
            description: listDescription,
            filters: listFilters,
            name: listName,
          },
        },
      });

      analytics.track(TRACKING_EVENTS.segment.editSaveClicked, {
        segmentId: `${dynamicList?.id}`,
      });

      await push({
        pathname: routes.engagement.audiences.dynamicLists.list.href(
          marketListingKey as string,
          `${dynamicList?.id}`
        ),
      });
    } catch (error) {
      formatAndShowError(error);
    }
  };

  return (
    <CreateOrEditDynamicListContext.Provider
      value={{
        contactsData: audiencesContactsData,
        contactsError: audiencesContactsError,
        contactsFilters,
        contactsLoading: audiencesContactsLoading,
        contactsNetworkStatus: audiencesContactsNetworkStatus,
        contactsOptions,
        createDynamicListLoading,
        dynamicList,
        listDescription,
        listFilters,
        listName,
        onClickClearAllContactFilters,
        onClickCreateDynamicList,
        onClickUpdateDynamicList,
        page,
        rowsPerPage,
        search,
        setContactsFilters: (newFilters) => {
          // When changing contact filters, reset page to 1
          setPage(1);
          setContactsFilters(newFilters);
        },
        setListDescription,
        setListFilters,
        setListName,
        setPage,
        setRowsPerPage,
        setSearch,
        updateDynamicListLoading,
      }}
    >
      {children}
    </CreateOrEditDynamicListContext.Provider>
  );
};
