import { ChangeEvent, Dispatch, SetStateAction } from 'react';
import analytics from '@analytics';
import { Typography, inputStyles } from '@ds';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/outline';
import { ContactsFilters } from '@/components/investors/search/global/context';

interface Props {
  filters: ContactsFilters;
  open: boolean;
  setFilters: Dispatch<SetStateAction<ContactsFilters>>;
  toggleOpen: (open: boolean) => void;
}

const ContactsFilterBeneficialOwnerSharesCount: React.ComponentType<Props> = ({
  filters,
  open,
  setFilters,
  toggleOpen,
}) => {
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;

    analytics.track('irm_contacts_filters_applied', {
      filter: 'beneficial_owner_shares_count',
    });

    // Parse the current value to get min and max
    const currentValue = filters.beneficialOwnerSharesCount || '';
    const parts = currentValue.split(',');
    let minShares = parts[0] || '';
    let maxShares = parts[1] || '';

    if (id === 'min_beneficial_owner_shares') {
      minShares = value;
    } else {
      maxShares = value;
    }

    // Construct the new value
    let newValue = '';
    if (minShares && maxShares) {
      newValue = `${minShares},${maxShares}`;
    } else if (minShares) {
      newValue = minShares;
    } else if (maxShares) {
      newValue = `0,${maxShares}`;
    }

    setFilters({
      ...filters,
      beneficialOwnerSharesCount: newValue || undefined,
    });
  };

  // Parse current values for display
  const currentValue = filters.beneficialOwnerSharesCount || '';
  const parts = currentValue.split(',');
  const minValue = parts[0] || '';
  const maxValue = parts[1] || '';

  return (
    <div>
      <div
        className="flex flex-row items-center justify-between py-4"
        onClick={() => toggleOpen(!open)}
      >
        <div className="text-base font-semibold">Beneficial owner shares</div>
        {open ? (
          <ChevronUpIcon className="h-5 w-5" />
        ) : (
          <ChevronDownIcon className="h-5 w-5" />
        )}
      </div>
      {open && (
        <div className="space-y-4 pb-4">
          <div>
            <Typography className="mb-2" variant="text-body-sm">
              Minimum shares
            </Typography>
            <input
              className={inputStyles}
              id="min_beneficial_owner_shares"
              placeholder="e.g. 1000"
              type="number"
              value={minValue}
              onChange={handleChange}
            />
          </div>
          <div>
            <Typography className="mb-2" variant="text-body-sm">
              Maximum shares
            </Typography>
            <input
              className={inputStyles}
              id="max_beneficial_owner_shares"
              placeholder="e.g. 10000"
              type="number"
              value={maxValue}
              onChange={handleChange}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ContactsFilterBeneficialOwnerSharesCount;
