import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import React from 'react';
import analytics from '@analytics';
import { dropdownStyles } from '@ds';
import { Listbox } from '@headlessui/react';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@heroicons/react/outline';
import clsx from 'clsx';
import {
  ContactsFilters,
  FilterHasEntityType,
} from '@/components/investors/search/global/context';

interface Props {
  filters: ContactsFilters;
  setFilters: Dispatch<SetStateAction<ContactsFilters>>;
}
type option = { id: string; label: string };

const options: option[] = [
  {
    id: 'none',
    label: 'No filter',
  },
  {
    id: 'linked-only',
    label: 'Has beneficial owner account',
  },
  {
    id: 'unlinked-only',
    label: 'No beneficial owner account',
  },
];

const ContactsFilterHasBeneficialOwnerAccount: React.ComponentType<Props> = ({
  filters,
  setFilters,
}) => {
  const handleChange = (option: option) => {
    analytics.track('irm_contacts_filters_applied', {
      filter: 'has-beneficial-owner-account',
    });

    setFilters({
      ...filters,
      hasBeneficialOwnerAccount: option.id as FilterHasEntityType,
    });
  };

  const [selectedOption, setSelectedOption] = useState(
    options.find((option) => option.id === filters.hasBeneficialOwnerAccount) ||
      options[0]
  );

  useEffect(() => {
    if (filters.hasBeneficialOwnerAccount !== selectedOption.id) {
      setSelectedOption(
        options.find(
          (option) => option.id === filters.hasBeneficialOwnerAccount
        ) || options[0]
      );
    }
  }, [filters, selectedOption]);

  return (
    <div className="relative">
      <Listbox value={selectedOption} onChange={handleChange}>
        <Listbox.Button className="relative w-full cursor-default rounded-lg bg-white py-2 pl-3 pr-10 text-left shadow-md focus:outline-none focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm">
          <span className="block truncate">{selectedOption.label}</span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronDownIcon
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </span>
        </Listbox.Button>
        <Listbox.Options className={dropdownStyles}>
          {options.map((option, optionIdx) => (
            <Listbox.Option
              key={optionIdx}
              className={({ active }) =>
                clsx(
                  'relative cursor-default select-none py-2 pl-10 pr-4',
                  active ? 'bg-amber-100 text-amber-900' : 'text-gray-900'
                )
              }
              value={option}
            >
              {({ selected }) => (
                <>
                  <span
                    className={clsx(
                      'block truncate',
                      selected ? 'font-medium' : 'font-normal'
                    )}
                  >
                    {option.label}
                  </span>
                  {selected ? (
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-amber-600">
                      <CheckIcon className="h-5 w-5" aria-hidden="true" />
                    </span>
                  ) : null}
                </>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </Listbox>
    </div>
  );
};

export default ContactsFilterHasBeneficialOwnerAccount;
