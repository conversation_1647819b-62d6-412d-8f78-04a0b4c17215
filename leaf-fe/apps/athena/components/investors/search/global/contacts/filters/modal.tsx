// DS V2
import { useEffect, useState, MouseE<PERSON>, FormEvent } from 'react';
import { Button, ModalRight, Typography } from '@ds';
import { FilterIcon } from '@heroicons/react/outline';
import {
  useExistingStaticListsQuery,
  useExistingTagsQuery,
} from '@/apollo/generated';
import ContactsFilterBeneficialOwnerSharesCount from '@/components/investors/search/global/contacts/filters/beneficial-owner-shares-count';
import ContactsFilterBrokers from '@/components/investors/search/global/contacts/filters/brokers';
import ContactFilterBrokersDisclosed from '@/components/investors/search/global/contacts/filters/brokers-disclosed';
import ContactSourceTypes from '@/components/investors/search/global/contacts/filters/contact-source-types';
import ContactsFilterHasBeneficialOwnerAccount from '@/components/investors/search/global/contacts/filters/has-beneficial-owner-account';
import HasEmailTypes from '@/components/investors/search/global/contacts/filters/has-email-types';
import ContactsFilterHasShareholding from '@/components/investors/search/global/contacts/filters/has-shareholding';
import ContactsFilterHNWTypes from '@/components/investors/search/global/contacts/filters/hnw-types';
import ContactsFilterHoldingLength from '@/components/investors/search/global/contacts/filters/holding-length';
import ContactsFilterHoldingSize from '@/components/investors/search/global/contacts/filters/holding-size';
import ContactsFilterHoldingSizeDisclosed from '@/components/investors/search/global/contacts/filters/holding-size-disclosed';
import ContactsFilterLinkedToInvestor from '@/components/investors/search/global/contacts/filters/linked-to-investor';
import ContactsFilterLinkedToShareholder from '@/components/investors/search/global/contacts/filters/linked-to-shareholder';
import ContactsFilterLocation from '@/components/investors/search/global/contacts/filters/location';
import ContactsFilterNewShareholders from '@/components/investors/search/global/contacts/filters/new-shareholders';
import StaticListTags from '@/components/investors/search/global/contacts/filters/static-list-tags';
import ContactsFilterSubscriptionStatus from '@/components/investors/search/global/contacts/filters/subscription-status';
import ContactsFilterTags from '@/components/investors/search/global/contacts/filters/tags';
import ContactsFilterTopHolders from '@/components/investors/search/global/contacts/filters/top-holders';
import ContactsFilterTradingActivity from '@/components/investors/search/global/contacts/filters/trading-activity';

import {
  ContactsFilters,
  useInvestorsSearchGlobal,
} from '@/components/investors/search/global/context';
import PermissionWrapper from '@/components/layouts/permission-wrapper';
import { useAlert } from '@/contexts/alert-context';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { defaultContactsFilters } from '@/contexts/shared-filter-context';
import { FLAGS } from '@/hooks/use-feature-toggles';
import { Permissions } from '@/hooks/use-permission';

const ContactsFilterModal: React.ComponentType = () => {
  const { currentCompanyProfileUser, isPremium, isUK } =
    useCurrentCompanyProfileUser();

  const {
    appliedContactsFiltersNum,
    contactsFilters,
    hasAppliedContactsFilters,
    investorSearchPush,
  } = useInvestorsSearchGlobal();

  // TAGS TODO: static lists
  const isStaticListsFlagEnabled =
    currentCompanyProfileUser.profile.featuresEnabled.includes(
      FLAGS.staticLists
    );

  // Preload tags before modal opens so it doesn't change height
  useExistingTagsQuery({ skip: isStaticListsFlagEnabled });
  useExistingStaticListsQuery({ skip: !isStaticListsFlagEnabled });

  const { formatAndShowError } = useAlert();

  const [open, toggleOpen] = useState(false);

  const applyFilters = async (e: MouseEvent | FormEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (currentFilters.minShares && parseInt(currentFilters.minShares) < 0) {
      return formatAndShowError(
        'Min Shareholding size must be equal to or greater than 0'
      );
    }

    if (currentFilters.maxShares && parseInt(currentFilters.maxShares) < 0) {
      return formatAndShowError(
        'Max Shareholding size must be equal to or greater than 0'
      );
    }

    if (
      currentFilters.maxShares &&
      currentFilters.minShares &&
      parseInt(currentFilters.maxShares) < parseInt(currentFilters.minShares)
    ) {
      return formatAndShowError(
        'Shareholding size max value must be equal to or greater than the min value'
      );
    }

    investorSearchPush({
      beneficial_owner_shares_count:
        currentFilters.beneficialOwnerSharesCount || '',
      brokers: currentFilters.brokers,
      contact_brokers_disclosed: currentFilters.brokersDisclosed || [],
      contact_location: currentFilters.location,
      contact_max_shares: currentFilters.maxShares || '',
      contact_max_shares_disclosed: currentFilters.maxSharesDisclosed || '',
      contact_min_shares: currentFilters.minShares || '',
      contact_min_shares_disclosed: currentFilters.minSharesDisclosed || '',
      has_beneficial_owner_account:
        currentFilters.hasBeneficialOwnerAccount === 'linked-only'
          ? 'true'
          : currentFilters.hasBeneficialOwnerAccount === 'unlinked-only'
          ? 'false'
          : currentFilters.hasBeneficialOwnerAccount,
      has_email: currentFilters.hasEmail,
      has_investor: currentFilters.hasInvestor,
      has_shareholding: currentFilters.hasShareholding,
      hnw_status: currentFilters.hnwStatus,
      lead_status: currentFilters.leadStatus,
      max_months: currentFilters.maxMonths,
      min_months: currentFilters.minMonths,
      newholder_status: currentFilters.newShareholderStatus,
      shareholder_status: currentFilters.shareholderStatus,
      sources: currentFilters.contactSource,
      static_list_ids: currentFilters.staticListIds,
      subscription_status: currentFilters.subscriptionStatus,
      tags: currentFilters.tags,
      tradingActivity: currentFilters.tradingActivity,
      tradingActivityDateRange: currentFilters.tradingActivityDateRange,
      traits: currentFilters.traits,
    }).then(() => toggleOpen(false));
  };

  const [showClearAll, setShowClearAll] = useState(false);

  const [currentFilters, setCurrentFilters] =
    useState<ContactsFilters>(contactsFilters);

  // Update current filters when set by the context
  useEffect(() => {
    setCurrentFilters(contactsFilters);
  }, [contactsFilters]);

  useEffect(() => {
    if (
      JSON.stringify(defaultContactsFilters) === JSON.stringify(currentFilters)
    ) {
      setShowClearAll(false);
    } else {
      setShowClearAll(true);
    }
  }, [currentFilters]);

  const [openEmailSubcriptions, toggleOpenEmailSubscriptions] = useState(false);
  const [openContactSource, toggleContactSource] = useState(false);
  const [openHasEmail, toggleHasEmail] = useState(false);
  const [openHoldingSize, toggleHoldingSize] = useState(false);
  const [openHoldingSizeDisclosed, toggleHoldingSizeDisclosed] =
    useState(false);
  const [openLocation, toggleOpenLocation] = useState(false);
  const [openTags, toggleOpenTags] = useState(false);
  const [openTradingActivity, toggleOpenTradingActivity] = useState(false);
  const [openHoldingTime, toggleHoldingTime] = useState(false);
  const [openBrokers, toggleBrokers] = useState(false);
  const [openBrokersDisclosed, toggleBrokersDisclosed] = useState(false);
  const [openHasShareholding, toggleHasShareholding] = useState(false);
  const [openBeneficialOwnerSharesCount, toggleBeneficialOwnerSharesCount] =
    useState(false);

  const getAppliedNum = () => {
    let totalAppliedContactsFilters = 0;

    const {
      beneficialOwnerSharesCount,
      contactSource,
      hasBeneficialOwnerAccount,
      hasEmail,
      hasInvestor,
      hnwStatus,
      hubSignUpsDaysAgo,
      leadStatus,
      location,
      maxShares,
      minShares,
      newShareholderStatus,
      shareholderStatus,
      staticListIds,
      subscriptionStatus,
      tags,
      tradingActivity,
    } = currentFilters;

    if (hasInvestor !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (hubSignUpsDaysAgo !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (leadStatus !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (shareholderStatus !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    const numOfFilteredSubscriptions = Array.isArray(subscriptionStatus)
      ? subscriptionStatus.length
      : 1;

    if (newShareholderStatus !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (hnwStatus !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (tradingActivity !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (minShares) {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (maxShares) {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    const numOfFilteredTags = Array.isArray(tags) ? tags.length : 1;
    const numOfFilteredStaticListIds = Array.isArray(staticListIds)
      ? staticListIds.length
      : 1;

    const numOfFilteredLocations = Array.isArray(location)
      ? location.length
      : 1;

    const numOfFilteredContactSources = Array.isArray(contactSource)
      ? contactSource.length
      : 1;

    if (hasEmail !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (hasBeneficialOwnerAccount !== 'none') {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    if (beneficialOwnerSharesCount) {
      totalAppliedContactsFilters = totalAppliedContactsFilters + 1;
    }

    return (
      totalAppliedContactsFilters +
      numOfFilteredStaticListIds +
      numOfFilteredTags +
      numOfFilteredLocations +
      numOfFilteredContactSources +
      numOfFilteredSubscriptions
    );
  };

  return (
    <>
      <Button
        LeadingIcon={() => <FilterIcon className="h-4 w-4" />}
        size="sm"
        variant={hasAppliedContactsFilters ? 'primary' : 'tertiary-color'}
        onClick={() => {
          toggleOpen(true);
        }}
      >
        {hasAppliedContactsFilters
          ? `Filter (${appliedContactsFiltersNum})`
          : 'Filter'}
      </Button>

      <ModalRight
        open={open}
        title="Contact filters"
        onClose={() => toggleOpen(false)}
      >
        <form className="space-y-6" onSubmit={applyFilters}>
          <div>
            <Typography className="text-gray-700" variant="text-heading-sm">
              Contact traits
            </Typography>
            <Typography className="text-gray-500" variant="text-body-sm">
              Filters that are applicable to all your contacts.
            </Typography>

            <div className="mt-4 space-y-2">
              <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
                <ContactsFilterLinkedToShareholder
                  filters={currentFilters}
                  setFilters={setCurrentFilters}
                />
              </PermissionWrapper>

              <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
                <ContactsFilterHNWTypes
                  filters={currentFilters}
                  setFilters={setCurrentFilters}
                />
              </PermissionWrapper>

              <ContactsFilterLinkedToInvestor
                filters={currentFilters}
                setFilters={setCurrentFilters}
              />
              <div className="divide-y divide-gray-200 px-[5px]">
                {isStaticListsFlagEnabled && (
                  <StaticListTags
                    filters={currentFilters}
                    open={openTags}
                    setFilters={setCurrentFilters}
                    toggleOpen={toggleOpenTags}
                  />
                )}
                {!isStaticListsFlagEnabled && (
                  <ContactsFilterTags
                    lgAlwaysOpen
                    filters={currentFilters}
                    open={openTags}
                    setFilters={setCurrentFilters}
                    toggleOpen={toggleOpenTags}
                  />
                )}

                <ContactSourceTypes
                  filters={currentFilters}
                  isPremium={isPremium}
                  open={openContactSource}
                  setFilters={setCurrentFilters}
                  toggleOpen={toggleContactSource}
                />

                {isPremium && !isUK && (
                  <div>
                    <ContactsFilterLocation
                      filters={currentFilters}
                      open={openLocation}
                      setFilters={setCurrentFilters}
                      toggleOpen={toggleOpenLocation}
                    />
                  </div>
                )}

                <HasEmailTypes
                  filters={currentFilters}
                  open={openHasEmail}
                  setFilters={setCurrentFilters}
                  toggleOpen={toggleHasEmail}
                />

                <div>
                  <ContactsFilterSubscriptionStatus
                    filters={currentFilters}
                    open={openEmailSubcriptions}
                    setFilters={setCurrentFilters}
                    toggleOpen={toggleOpenEmailSubscriptions}
                  />
                </div>
                <div className="h-px" />
              </div>
            </div>
          </div>

          {isPremium && (
            <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
              <div>
                <Typography className="text-gray-700" variant="text-heading-sm">
                  Shareholder traits
                </Typography>
                <Typography className="text-gray-500" variant="text-body-sm">
                  Only applicable to all shareholder contacts (past or present).
                  For contacts with multiple shareholdings linked, shareholder
                  traits are based on their combined net value.
                </Typography>

                <div className="mt-4 space-y-2">
                  <ContactsFilterNewShareholders
                    filters={currentFilters}
                    setFilters={setCurrentFilters}
                  />

                  <ContactsFilterTopHolders
                    filters={currentFilters}
                    setFilters={setCurrentFilters}
                  />

                  <div className="divide-y divide-gray-200 px-[5px]">
                    <ContactsFilterHasShareholding
                      filters={currentFilters}
                      open={openHasShareholding}
                      setFilters={setCurrentFilters}
                      toggleOpen={toggleHasShareholding}
                    />
                    <ContactsFilterTradingActivity
                      filters={currentFilters}
                      open={openTradingActivity}
                      setFilters={setCurrentFilters}
                      toggleOpen={toggleOpenTradingActivity}
                    />

                    <ContactsFilterHoldingSize
                      filters={currentFilters}
                      open={openHoldingSize}
                      setFilters={setCurrentFilters}
                      toggleOpen={toggleHoldingSize}
                    />

                    <ContactsFilterHoldingLength
                      filters={currentFilters}
                      open={openHoldingTime}
                      setFilters={setCurrentFilters}
                      toggleOpen={toggleHoldingTime}
                    />

                    <ContactsFilterBrokers
                      filters={currentFilters}
                      open={openBrokers}
                      setFilters={setCurrentFilters}
                      toggleOpen={toggleBrokers}
                    />

                    <div className="h-px" />
                  </div>
                </div>
              </div>
            </PermissionWrapper>
          )}

          {isPremium && (
            <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
              <div>
                <Typography className="text-gray-700" variant="text-heading-sm">
                  Beneficial owner traits
                </Typography>
                <Typography className="text-gray-500" variant="text-body-sm">
                  Filters based on beneficial owner account data and
                  shareholdings.
                </Typography>

                <div className="mt-4 space-y-2">
                  <ContactsFilterHasBeneficialOwnerAccount
                    filters={currentFilters}
                    setFilters={setCurrentFilters}
                  />

                  <div className="divide-y divide-gray-200 px-[5px]">
                    <ContactsFilterBeneficialOwnerSharesCount
                      filters={currentFilters}
                      open={openBeneficialOwnerSharesCount}
                      setFilters={setCurrentFilters}
                      toggleOpen={toggleBeneficialOwnerSharesCount}
                    />

                    <div className="h-px" />
                  </div>
                </div>
              </div>
            </PermissionWrapper>
          )}

          {isUK && (
            <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
              <div>
                <Typography className="text-gray-700" variant="text-heading-sm">
                  Self-reported holdings
                </Typography>
                <Typography className="text-gray-500" variant="text-body-sm">
                  Based on shareholdings disclosed by your investors on your
                  hub.
                </Typography>

                <div className="mt-2 divide-y px-[5px]">
                  <ContactsFilterHoldingSizeDisclosed
                    filters={currentFilters}
                    open={openHoldingSizeDisclosed}
                    setFilters={setCurrentFilters}
                    toggleOpen={toggleHoldingSizeDisclosed}
                  />

                  <ContactFilterBrokersDisclosed
                    filters={currentFilters}
                    open={openBrokersDisclosed}
                    setFilters={setCurrentFilters}
                    toggleOpen={toggleBrokersDisclosed}
                  />
                </div>
              </div>
            </PermissionWrapper>
          )}

          <div className="absolute bottom-0 left-0 flex w-full items-center justify-center gap-4 p-6">
            {showClearAll && (
              <Button
                className="w-full"
                variant="secondary-gray"
                onClick={() => setCurrentFilters(defaultContactsFilters)}
              >
                Clear all
              </Button>
            )}
            <Button className="w-full" onClick={applyFilters}>
              Apply {getAppliedNum() > 0 ? `(${getAppliedNum()})` : ''}
            </Button>
          </div>
        </form>
      </ModalRight>
    </>
  );
};

export default ContactsFilterModal;
