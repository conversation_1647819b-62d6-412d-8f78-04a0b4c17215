import { useCallback, useMemo } from 'react';
import { ParsedUrlQuery } from 'querystring';
import capitalize from 'lodash/capitalize';
import numeral from 'numeral';
import {
  ContactHnwStatuses,
  useExistingStaticListsQuery,
} from '@/apollo/generated';
import { ContactsFilters } from '@/components/investors/search/global/context';
import FilterChip from '@/components/investors/search/global/filter-chip';
import { defaultContactsFilters } from '@/contexts/shared-filter-context';

interface Props {
  contactsFilters: ContactsFilters;
  investorSearchPush: (newParams: ParsedUrlQuery) => Promise<boolean>;
  isImmutable?: boolean;
}

const ContactsFiltersApplied: React.ComponentType<Props> = ({
  contactsFilters,
  investorSearchPush,
  isImmutable = false,
}) => {
  const { data, loading } = useExistingStaticListsQuery();
  const investorLeadText = useMemo(() => {
    const { leadStatus } = contactsFilters;
    return leadStatus == 'investor-lead'
      ? 'Investor lead'
      : 'Not an investor lead';
  }, [contactsFilters]);

  const investorText = useMemo(() => {
    const { hasInvestor } = contactsFilters;
    return hasInvestor == 'linked-only'
      ? 'Investor hub member'
      : 'Not a hub member';
  }, [contactsFilters]);

  const shareholderText = useMemo(() => {
    const { shareholderStatus } = contactsFilters;
    switch (shareholderStatus) {
      case 'not-shareholder':
        return 'Investor lead';
      case 'past-shareholder':
        return 'Past shareholder';
      case 'nominated-shareholder':
        return 'Nominated shareholder';
      case 'nominated-shareholder-with-holding-info':
        return 'Nom. shareholder with holding';
      case 'nominated-shareholder-without-holding-info':
        return 'Nom. shareholder without holding';
      default:
        return 'Shareholder';
    }
  }, [contactsFilters]);

  const traitText = useMemo(() => {
    const { traits } = contactsFilters;
    switch (traits) {
      case 'top_20':
        return 'Top 20';
      case 'top_50':
        return 'Top 50';
      default:
        return '';
    }
  }, [contactsFilters]);

  const hasShareholdingText = useMemo(() => {
    const { hasShareholding } = contactsFilters;
    switch (hasShareholding) {
      case 'linked-only':
        return 'Has shareholding';
      case 'unlinked-only':
        return 'Doesn’t have shareholding';
      default:
        return '';
    }
  }, [contactsFilters]);

  const staticListData = useMemo(() => {
    const { staticListIds } = contactsFilters;
    if (loading) return [];
    if (staticListIds && data?.existingStaticLists) {
      const list = data?.existingStaticLists.filter((staticList) =>
        staticListIds.includes(staticList.id)
      );
      return list.length === 1 ? list[0] : list;
    }
    return [];
  }, [contactsFilters, data?.existingStaticLists, loading]);

  const renderBrokers = useCallback(() => {
    const { brokers } = contactsFilters;
    if (!brokers.length) return null;
    if (Array.isArray(brokers)) {
      return brokers.map((broker) => (
        <FilterChip
          key={broker}
          isImmutable={isImmutable}
          title={`Broker: ${broker}`}
          onClick={() =>
            investorSearchPush({ brokers: brokers.filter((b) => b !== broker) })
          }
        />
      ));
    }

    return (
      <FilterChip
        isImmutable={isImmutable}
        title={`Broker: ${brokers}`}
        onClick={() => investorSearchPush({ brokers: [] })}
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const renderBrokersDisclosed = useCallback(() => {
    const { brokersDisclosed } = contactsFilters;
    if (!(brokersDisclosed ?? []).length) return null;
    if (Array.isArray(brokersDisclosed)) {
      return brokersDisclosed.map((broker) => (
        <FilterChip
          key={broker}
          isImmutable={isImmutable}
          title={`Broker: ${broker}`}
          onClick={() =>
            investorSearchPush({
              contact_brokers_disclosed: brokersDisclosed.filter(
                (b) => b !== broker
              ),
            })
          }
        />
      ));
    }

    return (
      <FilterChip
        isImmutable={isImmutable}
        title={`Broker: ${brokersDisclosed}`}
        onClick={() => investorSearchPush({ contact_brokers_disclosed: [] })}
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const renderSubscriptions = useCallback(() => {
    const { subscriptionStatus: subscriptionStatuses } = contactsFilters;
    if (!subscriptionStatuses.length) return null;
    const subscriptionText = (subscriptionStatus: string) => {
      switch (subscriptionStatus) {
        case 'general':
          return 'Subscribed to general';
        case 'announcement':
          return 'Subscribed to announcements';
        case 'activity_update':
          return 'Subscribed to updates';
        case 'qa':
          return 'Subscribed to Q&A replies';
        default:
          return '';
      }
    };
    if (Array.isArray(subscriptionStatuses)) {
      return subscriptionStatuses.map((subscriptionStatus) => (
        <FilterChip
          key={subscriptionStatus}
          isImmutable={isImmutable}
          title={subscriptionText(subscriptionStatus)}
          onClick={() =>
            investorSearchPush({
              subscription_status: subscriptionStatuses.filter(
                (t) => t !== subscriptionStatus
              ),
            })
          }
        />
      ));
    }

    if (subscriptionStatuses === 'global-unsubscribed') {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title="Globally unsubscribed"
          onClick={() => investorSearchPush({ subscription_status: [] })}
        />
      );
    }
    if (subscriptionStatuses === 'suppressed') {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title="Email is suppressed"
          onClick={() => investorSearchPush({ subscription_status: [] })}
        />
      );
    }
    return (
      <FilterChip
        key={subscriptionStatuses}
        isImmutable={isImmutable}
        title={subscriptionText(subscriptionStatuses)}
        onClick={() => investorSearchPush({ subscription_status: [] })}
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const newShareholderText = useMemo(() => {
    const { newShareholderStatus } = contactsFilters;
    switch (newShareholderStatus) {
      case '30':
        return 'New shareholder < 30 days';
      case '60':
        return 'New shareholder < 60 days';
      case '90':
        return 'New shareholder < 90 days';
      default:
        return 'Shareholder';
    }
  }, [contactsFilters]);

  const hnwText = useMemo(() => {
    const {
      IdentifiedViaBehaviour,
      NominatedCertPending,
      NominatedCertVerified,
      NominatedWithoutCert,
    } = ContactHnwStatuses;

    const { hnwStatus } = contactsFilters;
    switch (hnwStatus) {
      case IdentifiedViaBehaviour:
        return 'High net worth behaviour';
      case NominatedCertPending:
        return 'Pending review';
      case NominatedCertVerified:
        return 'Verified';
      case NominatedWithoutCert:
        return 'Self-nominated';
      default:
        return 'Qualified investor status not indicated';
    }
  }, [contactsFilters]);

  const tradingActivityText = useMemo(() => {
    const { tradingActivity, tradingActivityDateRange } = contactsFilters;
    if (tradingActivity !== 'none') {
      const activity =
        tradingActivity === 'new' ? 'New movers' : capitalize(tradingActivity);

      const dateRange = ['7', '30', '90', '180'].includes(
        tradingActivityDateRange
      )
        ? `last ${tradingActivityDateRange} days`
        : `${tradingActivityDateRange.split(',')[0]} - ${
            tradingActivityDateRange.split(',')[1]
          }`;

      return `${activity} ${dateRange}`;
    }

    return '';
  }, [contactsFilters]);

  const lastThirtyText = useMemo(() => {
    const { hubSignUpsDaysAgo } = contactsFilters;
    switch (hubSignUpsDaysAgo) {
      case '30':
        return 'New hub sign ups < 30 Days';
      default:
        return '-';
    }
  }, [contactsFilters]);

  const renderTags = useCallback(() => {
    const { tags } = contactsFilters;
    if (!tags.length) return null;
    if (Array.isArray(tags)) {
      return tags.map((tag) => (
        <FilterChip
          key={tag}
          isImmutable={isImmutable}
          title={`Tagged: ${tag}`}
          onClick={() =>
            investorSearchPush({ tags: tags.filter((t) => t !== tag) })
          }
        />
      ));
    }
    return (
      <FilterChip
        isImmutable={isImmutable}
        title={`Tagged: ${tags}`}
        onClick={() => investorSearchPush({ tags: [] })}
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const renderStaticLists = useCallback(() => {
    const { staticListIds } = contactsFilters;

    if (!staticListData) return null;
    if (loading) return null;
    if (Array.isArray(staticListData)) {
      return staticListData.map((tag) => (
        <FilterChip
          key={tag.id}
          isImmutable={isImmutable}
          title={`Tagged: ${tag.name}`}
          onClick={() => {
            investorSearchPush({
              static_list_ids: staticListIds.filter((t) => t !== tag.id),
            });
          }}
        />
      ));
    }

    return (
      <FilterChip
        isImmutable={isImmutable}
        title={`Tagged: ${staticListData.name}`}
        onClick={() => investorSearchPush({ static_list_ids: [] })}
      />
    );
  }, [
    contactsFilters,
    staticListData,
    loading,
    isImmutable,
    investorSearchPush,
  ]);

  const renderLocations = useCallback(() => {
    const { location: locations } = contactsFilters;
    if (!locations.length) return null;
    if (Array.isArray(locations)) {
      return locations.map((location) => (
        <FilterChip
          key={location}
          isImmutable={isImmutable}
          title={`Location: ${location.toUpperCase()}`}
          onClick={() =>
            investorSearchPush({
              contact_location: locations.filter((t) => t !== location),
            })
          }
        />
      ));
    }
    return (
      <FilterChip
        isImmutable={isImmutable}
        title={`Location: ${(locations as string).toUpperCase()}`}
        onClick={() => investorSearchPush({ contact_location: [] })}
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const renderContactSource = useCallback(() => {
    const source_mapping = {
      beneficial_owners_import: 'From nominee unmasking',
      bulk_import: 'Uploaded from list',
      hub_signup: 'Signed up to your hub',
      manual_creation: 'Manually created',
      registry_import: 'Imported from registry',
      subscribe_form: 'Subscribed to email list',
    };

    const { contactSource: contactSources } = contactsFilters;
    if (!contactSources.length) return null;
    if (Array.isArray(contactSources)) {
      return contactSources.map((contactSource) => (
        <FilterChip
          key={contactSource}
          isImmutable={isImmutable}
          title={`Source of contact: ${source_mapping[contactSource]}`}
          onClick={() =>
            investorSearchPush({
              sources: contactSources.filter((c) => c !== contactSource),
            })
          }
        />
      ));
    }
    return (
      <FilterChip
        isImmutable={isImmutable}
        title={`Source of contact: ${source_mapping[contactSources]}`}
        onClick={() => investorSearchPush({ sources: [] })}
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const renderHasEmail = useCallback(() => {
    const email_mapping = {
      false: 'Doesn’t have email',
      suppressed: 'Email is suppressed',
      true: 'Email is valid',
    };

    const { hasEmail } = contactsFilters;
    if (!hasEmail || hasEmail == 'none') return null;

    return (
      <FilterChip
        isImmutable={isImmutable}
        title={`${email_mapping[hasEmail]}`}
        onClick={() => investorSearchPush({ has_email: 'none' })}
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const renderHasBeneficialOwnerAccount = useCallback(() => {
    const { hasBeneficialOwnerAccount } = contactsFilters;
    if (!hasBeneficialOwnerAccount || hasBeneficialOwnerAccount === 'none')
      return null;

    const label =
      hasBeneficialOwnerAccount === 'linked-only'
        ? 'Has beneficial owner account'
        : 'No beneficial owner account';

    return (
      <FilterChip
        isImmutable={isImmutable}
        title={label}
        onClick={() =>
          investorSearchPush({ has_beneficial_owner_account: 'none' })
        }
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const renderBeneficialOwnerSharesCount = useCallback(() => {
    const { beneficialOwnerSharesCount } = contactsFilters;
    if (!beneficialOwnerSharesCount) return null;

    const parts = beneficialOwnerSharesCount.split(',');
    const minShares = parts[0];
    const maxShares = parts[1];

    let title = 'Beneficial owner shares: ';
    if (minShares && maxShares) {
      title += `${numeral(minShares).format('0,0a')} - ${numeral(
        maxShares
      ).format('0,0a')}`;
    } else if (minShares) {
      title += `Min ${numeral(minShares).format('0,0a')}`;
    } else if (maxShares) {
      title += `Max ${numeral(maxShares).format('0,0a')}`;
    }

    return (
      <FilterChip
        isImmutable={isImmutable}
        title={title}
        onClick={() =>
          investorSearchPush({ beneficial_owner_shares_count: undefined })
        }
      />
    );
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const holdingSizeChip = useMemo(() => {
    const { maxShares, minShares } = contactsFilters;

    if (minShares && maxShares) {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title={`Holding size: Min ${numeral(minShares).format(
            '0,0a'
          )} - Max ${numeral(maxShares).format('0,0a')}`}
          onClick={() =>
            investorSearchPush({
              contact_max_shares: undefined,
              contact_min_shares: undefined,
            })
          }
        />
      );
    }

    if (minShares) {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title={`Holding size: Min ${numeral(minShares).format('0,0a')}`}
          onClick={() =>
            investorSearchPush({
              contact_max_shares: undefined,
              contact_min_shares: undefined,
            })
          }
        />
      );
    }

    if (maxShares) {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title={`Holding size: Max ${numeral(maxShares).format('0,0a')}`}
          onClick={() =>
            investorSearchPush({
              contact_max_shares: undefined,
              contact_min_shares: undefined,
            })
          }
        />
      );
    }
    return null;
  }, [contactsFilters, isImmutable, investorSearchPush]);

  const holdingSizeDisclosedChip = useMemo(() => {
    const { maxSharesDisclosed, minSharesDisclosed } = contactsFilters;

    if (minSharesDisclosed && maxSharesDisclosed) {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title={`Holding size disclosed: Min ${numeral(
            minSharesDisclosed
          ).format('0,0a')} - Max ${numeral(maxSharesDisclosed).format(
            '0,0a'
          )}`}
          onClick={() =>
            investorSearchPush({
              contact_max_shares_disclosed: undefined,
              contact_min_shares_disclosed: undefined,
            })
          }
        />
      );
    }

    if (minSharesDisclosed) {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title={`Holding size disclosed: Min ${numeral(
            minSharesDisclosed
          ).format('0,0a')}`}
          onClick={() =>
            investorSearchPush({
              contact_max_shares_disclosed: undefined,
              contact_min_shares_disclosed: undefined,
            })
          }
        />
      );
    }

    if (maxSharesDisclosed) {
      return (
        <FilterChip
          isImmutable={isImmutable}
          title={`Holding size disclosed: Max ${numeral(
            maxSharesDisclosed
          ).format('0,0a')}`}
          onClick={() =>
            investorSearchPush({
              contact_max_shares_disclosed: undefined,
              contact_min_shares_disclosed: undefined,
            })
          }
        />
      );
    }
    return null;
  }, [contactsFilters, isImmutable, investorSearchPush]);

  return (
    <div className="flex flex-wrap items-center gap-4 py-3">
      {contactsFilters.hubSignUpsDaysAgo !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={lastThirtyText}
          onClick={() => investorSearchPush({ hub_sign_ups_days_ago: 'none' })}
        />
      ) : null}
      {contactsFilters.leadStatus !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={investorLeadText}
          onClick={() => investorSearchPush({ lead_status: 'none' })}
        />
      ) : null}
      {contactsFilters.shareholderStatus !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={shareholderText}
          onClick={() => investorSearchPush({ shareholder_status: 'none' })}
        />
      ) : null}
      {contactsFilters.newShareholderStatus !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={newShareholderText}
          onClick={() => investorSearchPush({ newholder_status: 'none' })}
        />
      ) : null}
      {contactsFilters.hasInvestor !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={investorText}
          onClick={() => investorSearchPush({ has_investor: 'none' })}
        />
      ) : null}
      {contactsFilters.hnwStatus !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={hnwText}
          onClick={() => investorSearchPush({ hnw_status: 'none' })}
        />
      ) : null}
      {contactsFilters.traits !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={traitText}
          onClick={() => investorSearchPush({ traits: 'none' })}
        />
      ) : null}

      {contactsFilters.tradingActivity !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={tradingActivityText}
          onClick={() =>
            investorSearchPush({
              tradingActivity: 'none',
              tradingActivityDateRange: '30',
            })
          }
        />
      ) : null}
      {!!contactsFilters.minMonths &&
      contactsFilters.minMonths !== 'none' &&
      contactsFilters.minMonths != undefined ? (
        <FilterChip
          isImmutable={isImmutable}
          title={`Min months held: ${contactsFilters.minMonths}`}
          onClick={() => investorSearchPush({ min_months: undefined })}
        />
      ) : null}
      {!!contactsFilters.maxMonths &&
      contactsFilters.maxMonths !== 'none' &&
      contactsFilters.maxMonths != undefined ? (
        <FilterChip
          isImmutable={isImmutable}
          title={`Max months held: ${contactsFilters.maxMonths}`}
          onClick={() => investorSearchPush({ max_months: undefined })}
        />
      ) : null}

      {!!contactsFilters.hasShareholding &&
      contactsFilters.hasShareholding !== 'none' ? (
        <FilterChip
          isImmutable={isImmutable}
          title={hasShareholdingText}
          onClick={() => investorSearchPush({ has_shareholding: 'none' })}
        />
      ) : null}

      {holdingSizeChip}
      {holdingSizeDisclosedChip}
      {renderSubscriptions()}
      {renderTags()}
      {renderStaticLists()}
      {renderLocations()}
      {renderContactSource()}
      {renderHasEmail()}
      {renderHasBeneficialOwnerAccount()}
      {renderBeneficialOwnerSharesCount()}
      {renderBrokers()}
      {renderBrokersDisclosed()}
      {(JSON.stringify(defaultContactsFilters) !==
        JSON.stringify(contactsFilters) ||
        contactsFilters.hubSignUpsDaysAgo) &&
      !isImmutable ? (
        <div
          className="flex h-[32px] cursor-pointer items-center gap-1.5 rounded-lg bg-gray-100 px-2 py-1.5 text-sm text-gray-700"
          onClick={() => {
            investorSearchPush({
              beneficial_owner_shares_count: undefined,
              brokers: [],
              contact_brokers_disclosed: [],
              contact_location: [],
              contact_max_shares: undefined,
              contact_max_shares_disclosed: undefined,
              contact_min_shares: undefined,
              contact_min_shares_disclosed: undefined,
              contact_source: [],
              has_beneficial_owner_account: 'none',
              has_email: [],
              has_investor: 'none',
              has_shareholding: 'none',
              hnw_status: 'none',
              hub_sign_ups_days_ago: 'none',
              lead_status: 'none',
              max_months: undefined,
              min_months: undefined,
              newholder_status: 'none',
              shareholder_status: 'none',
              sources: [],
              static_list_ids: [],
              subscription_status: [],
              tags: [],
              tradingActivity: 'none',
              tradingActivityDateRange: '30',
              traits: 'none',
            });
          }}
        >
          Clear all
        </div>
      ) : null}
    </div>
  );
};

export default ContactsFiltersApplied;
