import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import analytics from '@analytics';
import orderBy from 'lodash/orderBy';
import { clearAutoSaveStates, useAutoSaveState } from '@leaf/helpers';
import {
  AllContactsQuery,
  DistributionChannelType,
  DistributionRecipientListType,
  CurrentCompanyDistributionSettingsQuery,
  FlowType,
  useAllContactsQuery,
  useCurrentCompanyDistributionSettingsQuery,
  useCreateDistributionSettingsEmailMutation,
  useUpsertDistributionSettingsMutation,
  useActivateCurrentCompanyDistributionSettingsMutation,
} from '@/apollo/generated';
import { useAlert } from '@/contexts/alert-context';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { getDefaultSocialPostTemplate } from '@/utils/distribution-helpers';

interface Props {
  children?: React.ReactNode;
}

interface AnnouncementsDistributionConfigurationContextProps {
  activeChannel?: DistributionChannelType; // To be used in conjuction on step 2, determine the template to edit
  // Step 1 is choosing the types and step 2 is edit template
  activeStep: number;
  addUnsubscribeModalOpen: boolean;
  allContacts: NonNullable<NonNullable<AllContactsQuery['allContacts']>>;
  allUniqueContacts: NonNullable<NonNullable<AllContactsQuery['allContacts']>>;
  allUsingRecommended: boolean;
  audienceList: DistributionRecipientListType[];
  currentHoldingOrder: 'desc' | 'asc';
  currentPage: number;
  currentPageContacts: NonNullable<
    NonNullable<AllContactsQuery['allContacts']>
  >;
  emailHtml: string;
  emailJson: string;
  existingSettings?: CurrentCompanyDistributionSettingsQuery;
  fromName: string;
  isAllContactsSelected: boolean;
  isLoading: boolean;
  // This is from useCurrentCompanyProfileUser
  linkedinSetupCompleted: boolean;
  loadingContacts: boolean;
  loadingExistingSettings: boolean;
  onActivate: (onSuccess: () => void) => Promise<void>;
  onManualSelection: () => void;
  onRecommendedSelection: () => void;
  onSaveEmailTemplate: (onSuccess: () => void) => Promise<void>;
  onSaveSelectedTypes: (onSuccess: () => void) => Promise<void>;
  // Flag to prompt user to use recommended settings (initial setup)
  promptRecommendedSettings: boolean;
  resetContactList: () => void;
  resetEmailTemplate: () => void;
  searchPhrase: string;
  selectedContactIds: string[];
  selectedEmailTypes: string[];
  selectedLinkedInTypes: string[];
  selectedTwitterTypes: string[];
  setActiveChannel: React.Dispatch<
    React.SetStateAction<DistributionChannelType | undefined>
  >;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  setAddUnsubscribeModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setAudienceList: React.Dispatch<
    React.SetStateAction<DistributionRecipientListType[]>
  >;
  setCurrentHoldingOrder: React.Dispatch<React.SetStateAction<'desc' | 'asc'>>;
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
  setEmailHtml: (value: string) => void;
  setEmailJson: (value: string) => void;
  setFromName: (value: string) => void;
  setSearchPhrase: React.Dispatch<React.SetStateAction<string>>;
  setSelectedContactIds: React.Dispatch<React.SetStateAction<string[]>>;
  setSelectedEmailTypes: React.Dispatch<React.SetStateAction<string[]>>;
  setSelectedLinkedInTypes: React.Dispatch<React.SetStateAction<string[]>>;
  setSelectedTwitterTypes: React.Dispatch<React.SetStateAction<string[]>>;
  setSubject: (value: string) => void;
  setTempAudienceList: React.Dispatch<
    React.SetStateAction<DistributionRecipientListType[]>
  >;
  setTotalCount: React.Dispatch<React.SetStateAction<number>>;
  skipSettingsRecommended: boolean;
  subject: string;
  tempAudienceList: DistributionRecipientListType[];
  totalCount: number;
  totalDuplicates: number;
  twitterSetupCompleted: boolean; // This is from useCurrentCompanyProfileUser
}
const AnnouncementsDistributionConfigurationContext =
  createContext<AnnouncementsDistributionConfigurationContextProps | null>(
    null
  );

export const useAnnouncementsDistributionConfigurationContext = () => {
  const context = useContext(AnnouncementsDistributionConfigurationContext);

  if (!context) {
    throw new Error(
      'Please use the announcements distribution configuration context within its provider'
    );
  }

  return context;
};

export const AnnouncementsDistributionConfigurationContextProvider: React.FC<
  Props
> = ({ children }) => {
  const { currentCompanyProfileUser } = useCurrentCompanyProfileUser();

  const defaultExcludedContacts: string[] = [];
  const defaultRecipientListType = [DistributionRecipientListType.All];

  const {
    profile: {
      announcementTypes: {
        recommendedValues: RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES,
      },
      socialConnection,
    },
  } = currentCompanyProfileUser;

  const linkedinSetupCompleted = !!socialConnection?.linkedinSetupCompleted;
  const twitterSetupCompleted = !!socialConnection?.twitterSetupCompleted;

  const [addUnsubscribeModalOpen, setAddUnsubscribeModalOpen] =
    React.useState(false);

  const [selectedEmailTypes, setSelectedEmailTypes] = useState<string[]>([]);
  const [selectedTwitterTypes, setSelectedTwitterTypes] = useState<string[]>(
    []
  );
  const [selectedLinkedInTypes, setSelectedLinkedInTypes] = useState<string[]>(
    []
  );
  const [activeStep, setActiveStep] = useState(1);
  const [activeChannel, setActiveChannel] = useState<
    DistributionChannelType | undefined
  >(undefined);

  const emailHtmlAutoSaveKey = `announcementsDistEmailConfigEmailHtml:${currentCompanyProfileUser.profile.id}`;
  const emailJsonAutoSaveKey = `announcementsDistEmailConfigEmailJson:${currentCompanyProfileUser.profile.id}`;
  const fromNameAutoSaveKey = `announcementsDistEmailConfigFromName:${currentCompanyProfileUser.profile.id}`;
  const subjectAutoSaveKey = `announcementsDistEmailConfigSubject:${currentCompanyProfileUser.profile.id}`;

  const [emailHtml, setEmailHtml] = useAutoSaveState('', emailHtmlAutoSaveKey);
  const [emailJson, setEmailJson] = useAutoSaveState('', emailJsonAutoSaveKey);
  const [fromName, setFromName] = useAutoSaveState('', fromNameAutoSaveKey);
  const [subject, setSubject] = useAutoSaveState('', subjectAutoSaveKey);
  // Recipients
  const [totalDuplicates, setTotalDuplicates] = useState(0);
  const [isAllContactsSelected, setIsAllContactsSelected] = useState(false);
  const [selectedContactIds, setSelectedContactIds] = useState<string[]>(
    defaultExcludedContacts
  );
  const [allContacts, setAllContacts] = useState<
    NonNullable<NonNullable<AllContactsQuery['allContacts']>>
  >([]);
  const [allUniqueContacts, setAllUniqueContacts] = useState<
    NonNullable<NonNullable<AllContactsQuery['allContacts']>>
  >([]);
  const [searchPhrase, setSearchPhrase] = useState('');
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [currentPageContacts, setCurrentPageContacts] = useState<
    NonNullable<NonNullable<AllContactsQuery['allContacts']>>
  >([]);
  const [audienceList, setAudienceList] = useState<
    DistributionRecipientListType[]
  >(defaultRecipientListType);
  const [tempAudienceList, setTempAudienceList] = useState(audienceList);
  const [currentHoldingOrder, setCurrentHoldingOrder] = useState<
    'desc' | 'asc'
  >('desc');
  const [skipSettingsRecommended, setSkipSettingsRecommended] = useState(false);

  const { formatAndShowError, showAlert } = useAlert();

  // Get existing settings
  const { data: existingSettings, loading: loadingExistingSettings } =
    useCurrentCompanyDistributionSettingsQuery({
      variables: {
        flowType: FlowType.Announcement,
      },
    });

  const [
    upsertDistributionSettings,
    { loading: loadingUpsertDistributionSettings },
  ] = useUpsertDistributionSettingsMutation({
    awaitRefetchQueries: true,
    refetchQueries: ['CurrentCompanyDistributionSettings', 'CustomEmails'],
  });

  const [
    createDistributionSettingsEmail,
    { loading: loadingCreateDistributionSettingsEmail },
  ] = useCreateDistributionSettingsEmailMutation({
    awaitRefetchQueries: true,
    refetchQueries: ['CurrentCompanyDistributionSettings', 'CustomEmails'],
  });

  const [
    activateCurrentCompanyDistributionSettings,
    { loading: loadingActivateCurrentCompanyDistributionSettings },
  ] = useActivateCurrentCompanyDistributionSettingsMutation();

  const onManualSelection = async () => {
    try {
      setSkipSettingsRecommended(true);
    } catch (error) {
      formatAndShowError(error);
    }
  };

  // Set existing settings
  useEffect(() => {
    if (existingSettings) {
      const {
        currentCompanyDistributionSettings: { email, linkedin, twitter },
      } = existingSettings;

      if (email && email.includedAnnouncementTypes) {
        setSelectedEmailTypes(email.includedAnnouncementTypes);
      } else {
        setSelectedEmailTypes([]);
      }

      if (linkedin && linkedin.includedAnnouncementTypes) {
        setSelectedLinkedInTypes(linkedin.includedAnnouncementTypes);
      } else {
        setSelectedLinkedInTypes([]);
      }

      if (twitter && twitter.includedAnnouncementTypes) {
        setSelectedTwitterTypes(twitter.includedAnnouncementTypes);
      } else {
        setSelectedTwitterTypes([]);
      }
    }
  }, [existingSettings]);

  // Set email settngs
  useEffect(() => {
    const emailSettings =
      existingSettings?.currentCompanyDistributionSettings.email?.emailSettings;

    if (emailSettings) {
      if (emailSettings.emailHtml) {
        if (emailHtml === '') {
          setEmailHtml(emailSettings.emailHtml);
        }
      }

      if (emailSettings.emailJson) {
        if (emailJson === '') {
          setEmailJson(emailSettings.emailJson);
        }
      }

      if (emailSettings.fromName) {
        setFromName(emailSettings.fromName);
      }

      if (emailSettings.subject) {
        setSubject(emailSettings.subject);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    existingSettings?.currentCompanyDistributionSettings.email?.emailSettings,
  ]);

  // Set audience list
  useEffect(() => {
    const recipientList =
      existingSettings?.currentCompanyDistributionSettings.email
        ?.recipientListType;

    if (recipientList) {
      setAudienceList(recipientList);
    }
  }, [
    existingSettings?.currentCompanyDistributionSettings.email
      ?.recipientListType,
  ]);

  useEffect(() => {
    setTempAudienceList(audienceList);
  }, [audienceList]);

  useEffect(() => {
    const excludedContacts =
      existingSettings?.currentCompanyDistributionSettings.email
        ?.excludedContacts;

    if (excludedContacts) {
      setSelectedContactIds(excludedContacts);
    }
  }, [
    existingSettings?.currentCompanyDistributionSettings.email
      ?.excludedContacts,
  ]);

  const emailUsingRecommended = useMemo(() => {
    return (
      RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES.length ===
        selectedEmailTypes.length &&
      RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES.every((v) =>
        selectedEmailTypes.includes(v)
      )
    );
  }, [RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES, selectedEmailTypes]);

  const linkedInUsingRecommended = useMemo(() => {
    return (
      RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES.length ===
        selectedLinkedInTypes.length &&
      RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES.every((v) =>
        selectedLinkedInTypes.includes(v)
      )
    );
  }, [RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES, selectedLinkedInTypes]);

  const twitterUsingRecommended = useMemo(() => {
    return (
      RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES.length ===
        selectedTwitterTypes.length &&
      RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES.every((v) =>
        selectedTwitterTypes.includes(v)
      )
    );
  }, [RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES, selectedTwitterTypes]);

  const allUsingRecommended = useMemo(() => {
    return (
      emailUsingRecommended &&
      ((linkedinSetupCompleted && linkedInUsingRecommended) ||
        !linkedinSetupCompleted) &&
      ((twitterSetupCompleted && twitterUsingRecommended) ||
        !twitterSetupCompleted)
    );
  }, [
    emailUsingRecommended,
    linkedinSetupCompleted,
    linkedInUsingRecommended,
    twitterSetupCompleted,
    twitterUsingRecommended,
  ]);

  const { data: contactsData, loading: loadingContacts } = useAllContactsQuery({
    skip: !tempAudienceList.length,
    variables: {
      options: {
        filters: [
          {
            key: 'campaign_email_audience_list',
            value: `announcement,${tempAudienceList.join(',')}`,
          },
          {
            key: 'has_email',
            value: 'true',
          },
        ],
      },
    },
  });

  useEffect(() => {
    if (contactsData?.allContacts) {
      let uniqueContacts = contactsData.allContacts
        .filter(
          (contact, index, array) =>
            array.findIndex((x) => x.id === contact.id) === index
        )
        .map((contact) => {
          return {
            ...contact,
            currentHolding:
              contact.shareholdings
                ?.map((sh) => sh.shareCount ?? 0)
                .reduce((acc, curr) => acc + curr, 0) ?? 0,
          };
        });

      setAllUniqueContacts(uniqueContacts);

      // Total selected contacts will include duplicates
      const totalSelectedContacts = contactsData.allContacts.filter((contact) =>
        selectedContactIds.includes(contact.id)
      );

      if (searchPhrase.length > 0) {
        uniqueContacts = uniqueContacts.filter(
          (contact) =>
            contact.email?.toLowerCase().includes(searchPhrase.toLowerCase()) ||
            (contact.firstName ?? '' + ' ' + contact.lastName ?? '')
              .toLowerCase()
              .includes(searchPhrase.toLowerCase())
        );
        setCurrentPage(1);
      }

      uniqueContacts = orderBy(
        uniqueContacts,
        'currentHolding',
        currentHoldingOrder
      );
      const duplicates =
        totalSelectedContacts.length - selectedContactIds.length;
      setAllContacts(uniqueContacts);
      setTotalCount(uniqueContacts.length);
      setTotalDuplicates(Math.sign(duplicates) === 1 ? duplicates : 0);
    }
  }, [
    contactsData,
    contactsData?.allContacts,
    currentHoldingOrder,
    searchPhrase,
    selectedContactIds,
  ]);

  useEffect(() => {
    setCurrentPageContacts(
      allContacts.slice((currentPage - 1) * 10, currentPage * 10)
    );
  }, [allContacts, currentPage]);

  useEffect(() => {
    if (allContacts.length > 0) {
      setIsAllContactsSelected(
        allContacts.every((contact) => selectedContactIds.includes(contact.id))
      );
      return;
    }

    setIsAllContactsSelected(false);
  }, [allContacts, selectedContactIds]);

  const onRecommendedSelection = async () => {
    try {
      setSelectedEmailTypes(RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES);
      setSelectedLinkedInTypes(RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES);
      setSelectedTwitterTypes(RECOMMENDED_ANNOUNCEMENT_TYPES_VALUES);
      setSkipSettingsRecommended(true);
    } catch (error) {
      formatAndShowError(error);
    }
  };

  // Only activate if any of the settings is not yet active
  // For socials only check if they are completely setup
  const onActivate = async (onSuccess: () => void) => {
    try {
      if (
        !existingSettings?.currentCompanyDistributionSettings.email?.isActive ||
        (linkedinSetupCompleted &&
          !existingSettings?.currentCompanyDistributionSettings.linkedin
            ?.isActive) ||
        (twitterSetupCompleted &&
          !existingSettings?.currentCompanyDistributionSettings.twitter
            ?.isActive)
      ) {
        await activateCurrentCompanyDistributionSettings({
          variables: { flowType: FlowType.Announcement },
        });
      }

      showAlert({ description: 'Settings saved', variant: 'success' });
      onSuccess();
    } catch (e) {
      formatAndShowError(e);
    }
  };

  // Save the selected types to automated distribution settings
  const onSaveSelectedTypes = async (onSuccess: () => void) => {
    try {
      analytics.track('distribution_automated_announcement_types_selected', {
        selectedEmailTypes,
        selectedLinkedInTypes,
        selectedTwitterTypes,
      });

      // Pass in default values for initial setup
      const emailInput = {
        excludedContacts: existingSettings?.currentCompanyDistributionSettings
          .email?.excludedContacts
          ? undefined
          : selectedContactIds,
        recipientListType: existingSettings?.currentCompanyDistributionSettings
          .email?.recipientListType
          ? undefined
          : audienceList,
      };

      // Pass in default values for socialPostTemplate on initial setup
      const linkedinInput = linkedinSetupCompleted
        ? {
            includedAnnouncementTypes: selectedLinkedInTypes,
            socialPostTemplate: existingSettings
              ?.currentCompanyDistributionSettings.linkedin?.socialPostTemplate
              ? undefined
              : getDefaultSocialPostTemplate('announcement'),
            usedDefaultTypes: linkedInUsingRecommended,
          }
        : undefined;

      // Pass in default values for socialPostTemplate on initial setup
      const twitterInput = twitterSetupCompleted
        ? {
            includedAnnouncementTypes: selectedTwitterTypes,
            socialPostTemplate: existingSettings
              ?.currentCompanyDistributionSettings.twitter?.socialPostTemplate
              ? undefined
              : getDefaultSocialPostTemplate('announcement'),
            usedDefaultTypes: twitterUsingRecommended,
          }
        : undefined;

      const { data: respData } = await upsertDistributionSettings({
        variables: {
          email: {
            includedAnnouncementTypes: selectedEmailTypes,
            usedDefaultTypes: emailUsingRecommended,
            ...emailInput,
          },
          flowType: FlowType.Announcement,
          linkedin: linkedinInput,
          twitter: twitterInput,
        },
      });

      // Create a default template if no emailSettings exist on email setting
      // This is normally happens during initial setup
      if (
        respData &&
        respData.upsertDistributionSettings.email &&
        !respData.upsertDistributionSettings.email.emailSettings
      ) {
        await createDistributionSettingsEmail({
          variables: {
            distributionSettingsEmail: {
              subject: null,
            },
            distributionSettingsId:
              respData.upsertDistributionSettings.email.id,
          },
        });
      }

      showAlert({ description: 'Settings saved', variant: 'success' });
      onSuccess();
    } catch (e) {
      formatAndShowError(e);
    }
  };

  const onSaveEmailTemplate = async (onSuccess: () => void) => {
    // ensure we load from the database next time
    clearAutoSaveStates(
      emailHtmlAutoSaveKey,
      emailJsonAutoSaveKey,
      fromNameAutoSaveKey,
      subjectAutoSaveKey
    );

    try {
      await upsertDistributionSettings({
        variables: {
          email: {
            emailSettings: { emailHtml, emailJson, fromName, subject },
            excludedContacts: selectedContactIds,
            recipientListType: audienceList,
          },
          flowType: FlowType.Announcement,
        },
      });

      showAlert({ description: 'Email template saved', variant: 'success' });
      onSuccess();
    } catch (e) {
      formatAndShowError(e);
    }
  };

  const resetContactList = () => {
    setSelectedContactIds(
      existingSettings?.currentCompanyDistributionSettings.email
        ?.excludedContacts || defaultExcludedContacts
    );
    setAudienceList(
      existingSettings?.currentCompanyDistributionSettings.email
        ?.recipientListType || defaultRecipientListType
    );
    setTempAudienceList(
      existingSettings?.currentCompanyDistributionSettings.email
        ?.recipientListType || defaultRecipientListType
    );
  };

  const resetEmailTemplate = () => {
    resetContactList();

    setEmailHtml(
      existingSettings?.currentCompanyDistributionSettings.email?.emailSettings
        ?.emailHtml || ''
    );
    setEmailJson(
      existingSettings?.currentCompanyDistributionSettings.email?.emailSettings
        ?.emailJson || ''
    );
    setFromName(
      existingSettings?.currentCompanyDistributionSettings.email?.emailSettings
        ?.fromName || ''
    );
    setSubject(
      existingSettings?.currentCompanyDistributionSettings.email?.emailSettings
        ?.subject || ''
    );
  };

  const isLoading =
    loadingActivateCurrentCompanyDistributionSettings ||
    loadingUpsertDistributionSettings ||
    loadingCreateDistributionSettingsEmail;

  return (
    <AnnouncementsDistributionConfigurationContext.Provider
      value={{
        activeChannel,
        activeStep,
        addUnsubscribeModalOpen,
        allContacts,
        allUniqueContacts,
        allUsingRecommended,
        audienceList,
        currentHoldingOrder,
        currentPage,
        currentPageContacts,
        emailHtml,
        emailJson,
        existingSettings,
        fromName,
        isAllContactsSelected,
        isLoading,
        linkedinSetupCompleted,
        loadingContacts,
        loadingExistingSettings,
        onActivate,
        onManualSelection,
        onRecommendedSelection,
        onSaveEmailTemplate,
        onSaveSelectedTypes,
        promptRecommendedSettings:
          !skipSettingsRecommended &&
          !existingSettings?.currentCompanyDistributionSettings.email,
        resetContactList,
        resetEmailTemplate,
        searchPhrase,
        selectedContactIds,
        selectedEmailTypes,
        selectedLinkedInTypes,
        selectedTwitterTypes,
        setActiveChannel,
        setActiveStep,
        setAddUnsubscribeModalOpen,
        setAudienceList,
        setCurrentHoldingOrder,
        setCurrentPage,
        setEmailHtml,
        setEmailJson,
        setFromName,
        setSearchPhrase,
        setSelectedContactIds,
        setSelectedEmailTypes,
        setSelectedLinkedInTypes,
        setSelectedTwitterTypes,
        setSubject,
        setTempAudienceList,
        setTotalCount,
        skipSettingsRecommended,
        subject,
        tempAudienceList,
        totalCount,
        totalDuplicates,
        twitterSetupCompleted,
      }}
    >
      {children}
    </AnnouncementsDistributionConfigurationContext.Provider>
  );
};
