// DS V2
import { Button, TextInput, Typography } from '@ds';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { useSetPasswordFromInvitationTokenMutation } from '@/apollo/generated';
import PasswordAcceptance from '@/components/common/password-acceptance';
import Auth from '@/components/layouts/auth';
import { useAlert } from '@/contexts/alert-context';
import { login } from '@/utils/auth-helpers';
import routes from '@/utils/routes';
import { passwordValidation } from '@/utils/validation-helpers';

interface FormData {
  password: string;
  passwordConfirmation: string;
}

interface Props {
  email: string;
  ticker: string;
  token: string;
}

const SetPassword: React.ComponentType<Props> = ({ email, ticker, token }) => {
  const [setPasswordFromInvitationToken] =
    useSetPasswordFromInvitationTokenMutation();

  const { formatAndShowError } = useAlert();

  const { formState, handleSubmit, register, watch } = useForm<FormData>({
    resolver: yupResolver(
      yup.object().shape({
        password: passwordValidation,
        passwordConfirmation: yup
          .string()
          .oneOf([yup.ref('password')], 'Password does not match.'),
      })
    ),
  });

  const onSubmit = handleSubmit(async (data) => {
    try {
      await setPasswordFromInvitationToken({
        variables: {
          password: data.password,
          passwordConfirmation: data.passwordConfirmation,
          token,
        },
      });

      await login(email, data.password);

      window.location.assign(routes.confirmDetails.href(ticker));
    } catch (error) {
      formatAndShowError(error);
    }
  });

  const password = watch('password');

  return (
    <Auth
      header={
        <div className="space-y-8">
          <Typography
            className="text-center sm:text-white"
            variant="text-display-lg"
          >
            Set your password
          </Typography>
          <Typography
            className="text-center sm:text-white"
            variant="text-body-md"
          >
            Set a password to log in and access your workspace
          </Typography>
        </div>
      }
    >
      <form className="space-y-8" onSubmit={onSubmit}>
        <div className="space-y-6">
          <TextInput
            {...register('password')}
            error={!!formState.errors.password?.message}
            helperText={formState.errors.password?.message}
            id="password"
            label="New password"
            testId="accept-invite-new-pwd-input"
            type="password"
          />
          <TextInput
            {...register('passwordConfirmation')}
            error={!!formState.errors.passwordConfirmation?.message}
            helperText={formState.errors.passwordConfirmation?.message}
            id="passwordConfirmation"
            label="Confirm password"
            testId="accept-invite-confirm-pwd-input"
            type="password"
          />
        </div>
        <PasswordAcceptance password={password} />
        <Button
          className="w-full"
          disabled={formState.isSubmitting}
          testId="accept-invite-set-pwd-submit"
          type="submit"
        >
          Update password
        </Button>
      </form>
    </Auth>
  );
};

export default SetPassword;
