// DS V2
import { useState } from 'react';
import { Button, Typography } from '@ds';
import Router from 'next/router';
import {
  CompanyProfileUserFromInvitationTokenQuery,
  useResendInvitationTokenMutation,
} from '@/apollo/generated';
import Auth from '@/components/layouts/auth';
import { useAlert } from '@/contexts/alert-context';
import { useContactFresh } from '@/contexts/contact-fresh-context';

interface Props {
  tokenData: NonNullable<
    CompanyProfileUserFromInvitationTokenQuery['companyProfileUserFromInvitationToken']
  >;
}

const RequestNewLink: React.ComponentType<Props> = ({ tokenData }) => {
  const [resendInvitationToken, { loading }] =
    useResendInvitationTokenMutation();
  const [submitted, toggleSubmitted] = useState(false);
  const { showContactFreshModal } = useContactFresh();

  const { showGenericError } = useAlert();

  const onSubmit = async () => {
    try {
      await resendInvitationToken({
        variables: {
          id: tokenData.id,
        },
      });

      toggleSubmitted(true);
    } catch {
      showGenericError();
    }
  };

  if (!submitted) {
    return (
      <Auth
        header={
          <div className="space-y-8">
            <Typography
              className="text-center text-gray-900 sm:text-white"
              variant="text-display-lg"
            >
              This link has expired
            </Typography>
            <Typography className="text-center text-gray-900 sm:text-4xl sm:text-white">
              You can create another activation link by clicking on the Request
              button below
            </Typography>
          </div>
        }
      >
        <div className="space-y-8">
          <div className="space-y-2">
            <Typography variant="text-heading-sm">Your email</Typography>
            <Typography variant="text-body-md">
              {tokenData.user.email}
            </Typography>
          </div>
          <Button
            className="w-full"
            disabled={loading}
            type="button"
            onClick={onSubmit}
          >
            Request another link
          </Button>
          <div>
            <Typography className="text-center" variant="text-body-md">
              If the issue persists, please{' '}
              <span
                className="text-primary-green-dark underline"
                role="button"
                tabIndex={-1}
                onClick={() => showContactFreshModal(1)}
              >
                contact support.
              </span>
            </Typography>
          </div>
        </div>
      </Auth>
    );
  }

  return (
    <div className="bg-amplify-green-700 flex min-h-full flex-col space-y-8 px-4 py-8 sm:px-0 sm:py-32">
      <picture>
        <img
          alt="InvestorHub logo"
          className="mx-auto h-10 w-auto sm:h-[60px]"
          src="https://storage.googleapis.com/leaf-prod/logos/investor_hub_logo_white.png"
        />
      </picture>
      <Typography className="text-center text-white" variant="text-display-lg">
        {`We've sent you a new link`}
      </Typography>
      <Typography className="text-center text-white" variant="text-body-md">
        Please check your email for a new activation link
      </Typography>
      <div className="sm:self-center">
        <Button
          className="w-full sm:w-auto"
          disabled={loading}
          variant="secondary-gray"
          onClick={async () => {
            await Router.push(
              process.env.NEXT_PUBLIC_APHRODITE_URI || 'https://investorhub.com'
            );
          }}
        >
          Return to InvestorHub
        </Button>
      </div>
    </div>
  );
};

export default RequestNewLink;
