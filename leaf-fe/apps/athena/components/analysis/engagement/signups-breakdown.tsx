import React, { useCallback, useMemo, useState } from 'react';
import { Typography } from '@ds';
import numeral from 'numeral';
import {
  Cell,
  Label,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
} from 'recharts';

import EngagementAnalyticsEmpty from '@/components/analysis/engagement/empty';
import { useEngagementAnalyticsInvestorHubContext } from '@/components/analysis/engagement/investorhub-context';
import EngagementAnalyticsLoading from '@/components/analysis/engagement/loading';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';

const chartColors = {
  'Existing shareholders': '#FAC515',
  Leads: '#12B76A',
  'Nominated shareholders': '#0BA5EC',
  'Past shareholders': '#875BF7',
};

const InvestorHubSignupsBreakdown: React.ComponentType = () => {
  const { isPremium } = useCurrentCompanyProfileUser();
  const [focussedCell, setFocussedCell] = useState<string | undefined>(
    undefined
  );
  const { dateRangeText, engagementAnalyticsInvestorHubData, error, loading } =
    useEngagementAnalyticsInvestorHubContext();

  const data = useMemo(() => {
    let items = [
      {
        name: 'Leads',
        value:
          engagementAnalyticsInvestorHubData?.investorHubSignupBreakdown
            .leads ?? 0,
      },

      {
        name: 'Nominated shareholders',
        value:
          engagementAnalyticsInvestorHubData?.investorHubSignupBreakdown
            .nominatedShareholders ?? 0,
      },
    ];
    if (isPremium) {
      items = [
        ...items,
        {
          name: 'Past shareholders',
          value:
            engagementAnalyticsInvestorHubData?.investorHubSignupBreakdown
              .pastShareholders ?? 0,
        },
        {
          name: 'Existing shareholders',
          value:
            engagementAnalyticsInvestorHubData?.investorHubSignupBreakdown
              .existingShareholders ?? 0,
        },
      ];
    }
    return items;
  }, [
    engagementAnalyticsInvestorHubData?.investorHubSignupBreakdown,
    isPremium,
  ]);

  const getFocusColor = React.useCallback(
    (name: string) => {
      if (!focussedCell) {
        return chartColors[`${name as keyof typeof chartColors}`];
      }
      if (focussedCell && focussedCell === name) {
        return chartColors[`${name as keyof typeof chartColors}`];
      } else {
        return chartColors[`${name as keyof typeof chartColors}`] + '60';
      }
    },
    [focussedCell]
  );

  const renderContent = useCallback(() => {
    if (loading) {
      return <EngagementAnalyticsLoading />;
    }

    if (error) {
      return <EngagementAnalyticsEmpty />;
    }

    const CustomTooltip: React.ComponentType<
      TooltipProps<number, string> & { total: number }
    > = (all) => {
      const { active, payload, total } = all;

      if (active && payload && payload.length) {
        const currentData = payload[0];

        return (
          <div className="w-[240px] rounded-lg bg-gray-900 p-3 text-white shadow-lg">
            <Typography className="mb-1" variant="text-caption-bold">
              Sign ups
            </Typography>
            <div className="flex justify-between">
              <div className="flex items-center gap-1">
                <div
                  className="h-3 w-3"
                  style={{
                    background:
                      chartColors[
                        `${currentData.name as keyof typeof chartColors}`
                      ],
                  }}
                />
                <Typography variant="text-caption">
                  {currentData.name}
                </Typography>
              </div>
              <Typography variant="text-caption">
                {currentData.value} (
                {currentData.value
                  ? numeral(currentData.value / total).format('0,0.[00]%')
                  : 0}{' '}
                )
              </Typography>
            </div>
          </div>
        );
      }

      return null;
    };

    const total = data.reduce((acc, cur) => acc + cur.value, 0);

    const moreThanOneValue = data.filter((v) => v.value > 0).length > 1;

    return (
      <div className="flex min-h-[300px] flex-col items-center gap-6 p-4">
        <div className="h-[200px] w-full transition-colors">
          <ResponsiveContainer width="100%">
            <PieChart>
              <Tooltip
                content={<CustomTooltip total={total} />}
                cursor={{ stroke: 'red', strokeWidth: 2 }}
              />
              <Pie
                data={data}
                dataKey="value"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={moreThanOneValue ? 2 : 0}
              >
                <Label
                  className="font-semibold text-gray-900"
                  position="center"
                >
                  {total}
                </Label>
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={getFocusColor(entry.name)}
                    onMouseEnter={() => setFocussedCell(entry.name)}
                    onMouseLeave={() => setFocussedCell(undefined)}
                  />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="w-full p-4">
          {data.map(({ name, value }) => {
            return (
              <div key={name} className="flex justify-between">
                <div className="flex items-center gap-2">
                  <div
                    className="h-3 w-3"
                    style={{
                      background:
                        chartColors[`${name as keyof typeof chartColors}`],
                    }}
                  />
                  <Typography className="text-gray-700" variant="text-label-sm">
                    {name}
                  </Typography>
                </div>
                <Typography className="text-gray-700" variant="text-body-sm">
                  {value}
                </Typography>
              </div>
            );
          })}
        </div>
      </div>
    );
  }, [loading, error, data, getFocusColor]);

  return (
    <div className="flex-1 rounded-lg border border-gray-200 bg-white">
      <div className="border-b border-b-gray-200 px-6 py-5">
        <Typography className="text-gray-900" variant="text-heading-sm">
          Sign ups breakdown{' '}
          <span className="lowercase">{dateRangeText.chart}</span>
        </Typography>
      </div>
      {renderContent()}
    </div>
  );
};

export default InvestorHubSignupsBreakdown;
