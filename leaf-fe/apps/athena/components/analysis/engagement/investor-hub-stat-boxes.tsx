import React from 'react';
import { StatCard } from '@ds';
import { EyeIcon, UserAddIcon } from '@heroicons/react/outline';
import { UserIcon } from '@heroicons/react/solid';
import { useRouter } from 'next/router';

import { useEngagementAnalyticsInvestorHubContext } from '@/components/analysis/engagement/investorhub-context';
import routes from '@/utils/routes';

const EngagementAnalyticsInvestorHubStatBoxes: React.ComponentType = () => {
  const router = useRouter();
  const { dateRangeText, engagementAnalyticsInvestorHubData, error, loading } =
    useEngagementAnalyticsInvestorHubContext();

  return (
    <div className="w-full grid-cols-3 gap-6 space-y-4 lg:grid lg:space-y-0">
      <div className="col-span-1 flex w-full">
        <StatCard
          badgeNum={
            engagementAnalyticsInvestorHubData?.investorHubAnalytics
              .signupsDifference ?? 0
          }
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-green-50 p-3.5">
              <UserAddIcon className="h-5 w-5 text-green-600" />
            </div>
          }
          loading={loading}
          statNum={
            engagementAnalyticsInvestorHubData?.investorHubAnalytics
              .totalSignups ?? 0
          }
          timePeriodText={dateRangeText.statBox}
          title="Investor hub sign ups"
          tooltipText="Investors who have signed up to the investor hub"
          onClickAction={() =>
            router.push({
              pathname: routes.investors.search.href(
                router.query.marketListingKey as string
              ),
              query: {
                has_investor: 'linked-only',
                hub_sign_ups_days_ago: '30',
              },
            })
          }
        />
      </div>
      <div className="col-span-1 flex w-full">
        <StatCard
          badgeNum={
            engagementAnalyticsInvestorHubData?.investorHubAnalytics
              .uniqueVisitorsDifference ?? 0
          }
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-cyan-50 p-3.5">
              <UserIcon className="h-5 w-5 text-cyan-600" />
            </div>
          }
          loading={loading}
          statNum={
            engagementAnalyticsInvestorHubData?.investorHubAnalytics
              .totalUniqueVisitors ?? 0
          }
          timePeriodText={dateRangeText.statBox}
          title="Unique visitors"
          tooltipText="Unique visits to your investor hub"
        />
      </div>
      <div className="col-span-1 flex w-full">
        <StatCard
          badgeNum={
            engagementAnalyticsInvestorHubData?.investorHubAnalytics
              .totalViewsDifference ?? 0
          }
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-cyan-50 p-3.5">
              <EyeIcon className="h-5 w-5 text-cyan-600" />
            </div>
          }
          loading={loading}
          statNum={
            engagementAnalyticsInvestorHubData?.investorHubAnalytics
              .totalViews ?? 0
          }
          timePeriodText={dateRangeText.statBox}
          title="Views"
          tooltipText="Total page views, including multiple views from the same user"
        />
      </div>
    </div>
  );
};

export default EngagementAnalyticsInvestorHubStatBoxes;
