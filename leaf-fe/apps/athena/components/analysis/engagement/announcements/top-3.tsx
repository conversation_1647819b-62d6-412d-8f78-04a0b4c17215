import React from 'react';
import {
  ParagraphAlignLeftIcon,
  Typography,
  VideoCameraIcon,
  ArrowUpIcon,
  TwitterIcon,
  LinkedinIcon,
  Tooltip,
  Button,
} from '@ds';
import {
  ChatIcon,
  EyeIcon,
  ClipboardIcon,
  AtSymbolIcon,
} from '@heroicons/react/outline';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { EngagementAnalyticsAnnouncementsQuery } from '@/apollo/generated';
import { useEngagementAnalyticsAnnouncementsContext } from '@/components/analysis/engagement/announcements/context';
import EngagementAnalyticsComponentWrapper from '@/components/analysis/engagement/engagement-analytics-component-wrapper';
import TableCellRowLinkWrapper from '@/components/utils/tables/link-wrapper';
import TableHeader, { Header } from '@/components/utils/tables/table-header';
import routes from '@/utils/routes';

const EngagementAnalyticsAnnouncementsTop3: React.ComponentType = () => {
  const {
    dateRangeText,
    engagementAnalyticsAnnouncementsData,
    error,
    loading,
  } = useEngagementAnalyticsAnnouncementsContext();

  const {
    query: { marketListingKey },
  } = useRouter();

  const { topThreeViewedAnnouncements } =
    engagementAnalyticsAnnouncementsData ?? {};

  const HEADERS: Header[] = [
    {
      className: 'w-[45%] md:min-w-[250px] ld:min-w-[360px] whitespace-nowrap',
      label: 'Announcement name',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Views',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Questions',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Likes',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Survey',
    },
    {
      className: 'w-[11%] whitespace-nowrap',
      label: 'Distribution',
    },
  ];

  const renderAnnouncementRow = (
    announcement: EngagementAnalyticsAnnouncementsQuery['topThreeViewedAnnouncements'][0],
    index: number
  ) => {
    const href = routes.engagement.interactiveMedia.announcement.href(
      marketListingKey as string,
      announcement.id
    );
    return (
      <tr
        key={announcement.id}
        className="table-row border-b bg-white text-gray-500 first:border-t last:border-0 hover:cursor-pointer hover:bg-gray-50 md:first:border-t-0"
      >
        <td className="table-cell py-3 pl-3 text-gray-700 lg:pl-4">
          <TableCellRowLinkWrapper
            href={routes.engagement.interactiveMedia.announcement.href(
              marketListingKey as string,
              announcement.id
            )}
          >
            <div className="flex items-center gap-6">
              <Typography className="text-gray-900" variant="text-heading-md">
                {index + 1}
              </Typography>
              <div>
                <Typography
                  className="max-w-[400px] truncate whitespace-nowrap"
                  variant="text-button-sm"
                >
                  {announcement.header}
                </Typography>
                {!!(
                  announcement.socialVideoUrl ||
                  announcement.summary ||
                  announcement.videoUrl
                ) && (
                  <div className="mt-2 flex gap-2 text-gray-700">
                    {(announcement.socialVideoUrl || announcement.videoUrl) && (
                      <VideoCameraIcon className="h-5 w-5" />
                    )}
                    {announcement.summary && (
                      <ParagraphAlignLeftIcon className="h-5 w-5" />
                    )}
                  </div>
                )}
              </div>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <EyeIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {announcement.totalViewCountFromTimePeriod.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <ChatIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {announcement.totalQuestionCount.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <ArrowUpIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {announcement.likes.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <ClipboardIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {announcement.totalSurveyResponses.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2.5">
              {!announcement.distributedSocial?.twitterPostId &&
              !announcement.email &&
              !announcement.distributedSocial?.linkedinPostId
                ? '-'
                : null}
              {announcement.email ? (
                <Tooltip
                  className="min-w-[170px] max-w-[220px]"
                  content={
                    <>
                      <Typography className="text-white" variant="text-caption">
                        {`Email sent ${
                          announcement.email.sentAt
                            ? dayjs(announcement.email.sentAt).format(
                                'DD/MM/YYYY'
                              )
                            : null
                        }`}
                      </Typography>
                      <Typography
                        className="truncate text-white"
                        variant="text-caption"
                      >
                        {announcement.email.subject
                          ? announcement.email.subject.replace(
                              '{{ announcement_title }}',
                              announcement.header
                            )
                          : null}
                      </Typography>
                    </>
                  }
                >
                  <div className="cursor-pointer">
                    <AtSymbolIcon className="h-4 w-4 stroke-chart-orange-dark" />
                  </div>
                </Tooltip>
              ) : null}
              {announcement.distributedSocial?.twitterPostId &&
              announcement.distributedSocial?.twitterPostedAt ? (
                <Tooltip
                  className="min-w-[170px] max-w-[220px]"
                  description={`Posted to Twitter ${dayjs(
                    announcement.distributedSocial.twitterPostedAt
                  ).format('DD/MM/YYYY')}`}
                  place="top-start"
                >
                  <div className="cursor-pointer">
                    <TwitterIcon className="h-4 w-4 text-[#1DA1F2]" />
                  </div>
                </Tooltip>
              ) : null}
              {announcement.distributedSocial?.linkedinPostId &&
              announcement.distributedSocial?.linkedinPostedAt ? (
                <Tooltip
                  className="min-w-[170px] max-w-[220px]"
                  description={`Posted to LinkedIn ${dayjs(
                    announcement.distributedSocial.linkedinPostedAt
                  ).format('DD/MM/YYYY')}`}
                  place="top-start"
                >
                  <div className="cursor-pointer">
                    <LinkedinIcon className="h-4 w-4 text-blue-700" />
                  </div>
                </Tooltip>
              ) : null}
            </div>
          </TableCellRowLinkWrapper>
        </td>
      </tr>
    );
  };

  return (
    <EngagementAnalyticsComponentWrapper
      cta={
        <Button
          href={routes.engagement.interactiveMedia.announcements.href(
            marketListingKey as string
          )}
          variant="secondary-gray"
        >
          View all announcements
        </Button>
      }
      dateRangeText={dateRangeText.chart}
      error={!!error}
      loading={!!loading}
      title={`Your top ${
        topThreeViewedAnnouncements?.length ?? '3'
      } viewed announcements`}
    >
      {/* Table */}
      <div
        data-private
        className="min-w-full overflow-hidden rounded-lg bg-white"
      >
        <div className="overflow-x-auto">
          <div className="sticky top-[55px] z-10 md:border-b"></div>
          <table className="table min-w-full">
            <TableHeader
              headers={HEADERS}
              textColor="text-gray-500"
              textVariant="text-button-sm"
            />
            <tbody className="table-row-group">
              {topThreeViewedAnnouncements && topThreeViewedAnnouncements.length
                ? topThreeViewedAnnouncements.map((announcement, index) =>
                    renderAnnouncementRow(announcement, index)
                  )
                : null}
            </tbody>
          </table>
        </div>
      </div>
    </EngagementAnalyticsComponentWrapper>
  );
};

export default EngagementAnalyticsAnnouncementsTop3;
