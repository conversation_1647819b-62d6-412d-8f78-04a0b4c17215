import React, { useMemo, useState } from 'react';
import { <PERSON>ton, Checkbox, Tick, TickProps, Typography } from '@ds';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/router';
import numeral from 'numeral';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  LegendProps,
  Line,
  ResponsiveContainer,
  Tooltip as ChartTooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import { CategoricalChartFunc } from 'recharts/types/chart/generateCategoricalChart';
import { Payload } from 'recharts/types/component/DefaultLegendContent';
import { DataKey } from 'recharts/types/util/types';
import { useEngagementAnalyticsAnnouncementsContext } from '@/components/analysis/engagement/announcements/context';
import EngagementAnalyticsEmpty from '@/components/analysis/engagement/empty';
import EngagementAnalyticsComponentWrapper from '@/components/analysis/engagement/engagement-analytics-component-wrapper';
import EngagementAnalyticsLoading from '@/components/analysis/engagement/loading';
import useBreakpoint from '@/components/pdf-viewer/use-breakpoint';
import { INTERCOM_ARTICLES } from '@/utils/intercom-articles';
import routes from '@/utils/routes';
import type {
  NameType,
  ValueType,
} from 'recharts/types/component/DefaultTooltipContent';

const likesColor = '#86CB3C';
const surveysColor = '#4F7A21';
const questionsColor = '#CEEAB0';
const viewsColor = '#384250';
const commsColor = '#384250';
const newTooltipThreshold = 150;

interface ModifiedPayload extends Payload {
  dataKey: DataKey<string>;
  payload: Payload['payload'];
}
interface ModifiedLegendProps extends LegendProps {
  payload?: Array<ModifiedPayload>;
}

type Announcement = {
  date: string;
  header: string;
  id: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomizedDot = (props: any) => {
  const { cx, cy, payload } = props;

  if (
    !cx ||
    !cy ||
    !(payload?.commsWithoutSummary || payload?.commsWithSummary)
  )
    return null;

  return (
    <svg
      fill="none"
      height="20"
      viewBox="0 0 20 20"
      width="20"
      x={cx - 10}
      xmlns="http://www.w3.org/2000/svg"
      y={cy - 10}
    >
      {payload?.commsWithSummary ? (
        <>
          <path
            d="M10 3.75002C10 3.4508 9.82215 3.18021 9.54747 3.06153C9.2728 2.94286 8.95387 2.99879 8.73598 3.20387L4.70257 7.00002H3.16724C2.85725 7.00002 2.5792 7.19074 2.46756 7.47993C2.16534 8.26287 2 9.11302 2 10C2 10.887 2.16534 11.7372 2.46756 12.5201C2.5792 12.8093 2.85725 13 3.16724 13H4.70257L8.73598 16.7962C8.95387 17.0012 9.2728 17.0572 9.54747 16.9385C9.82215 16.8198 10 16.5492 10 16.25V3.75002Z"
            fill="#111927"
          />
          <path
            d="M15.9498 5.05029C15.6569 4.7574 15.182 4.7574 14.8891 5.05029C14.5962 5.34318 14.5962 5.81806 14.8891 6.11095C17.037 8.25883 17.037 11.7412 14.8891 13.8891C14.5962 14.182 14.5962 14.6569 14.8891 14.9498C15.182 15.2427 15.6569 15.2427 15.9498 14.9498C18.6834 12.2161 18.6834 7.78396 15.9498 5.05029Z"
            fill="#111927"
          />
          <path
            d="M13.8287 7.1716C13.5358 6.8787 13.0609 6.8787 12.768 7.1716C12.4751 7.46449 12.4751 7.93936 12.768 8.23226C13.7443 9.20857 13.7443 10.7915 12.768 11.7678C12.4751 12.0607 12.4751 12.5356 12.768 12.8285C13.0609 13.1213 13.5358 13.1213 13.8287 12.8285C15.3908 11.2664 15.3908 8.73369 13.8287 7.1716Z"
            fill="#111927"
          />
        </>
      ) : (
        <path
          d="M15.9283 4.69684C18.8573 7.62577 18.8573 12.3745 15.9283 15.3034M13.7189 6.90662C15.4274 8.61516 15.4274 11.3853 13.7189 13.0938M5.625 6.87518L9.55806 2.94212C9.95179 2.5484 10.625 2.82725 10.625 3.38407V16.6163C10.625 17.1731 9.95179 17.452 9.55806 17.0582L5.625 13.1252H3.75755C3.02476 13.1252 2.33825 12.7031 2.14364 11.9967C1.96854 11.361 1.875 10.6915 1.875 10.0002C1.875 9.30886 1.96854 8.63938 2.14364 8.00371C2.33825 7.29724 3.02476 6.87518 3.75755 6.87518H5.625Z"
          stroke="#0F172A"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        />
      )}
    </svg>
  );
};

const CustomTooltip = ({
  active,
  label,
  payload,
  showComms,
  showLikes,
  showQuestions,
  showSurveys,
  showViews,
  ticker,
}: TooltipProps<ValueType, NameType> & {
  showComms: boolean;
  showLikes: boolean;
  showQuestions: boolean;
  showSurveys: boolean;
  showViews: boolean;
  ticker: string;
}) => {
  if (active && payload && payload.length) {
    const views = payload.find((p) => p.dataKey === 'totalViews')?.value;

    const hasViews = views !== null && views !== undefined;

    return (
      <div
        className="w-[240px] space-y-1 rounded-lg bg-gray-900 p-4"
        id="custom-tooltip"
      >
        <Typography className="text-white" variant="text-caption-bold">
          {`${dayjs(label).format('D MMM YY')}`}
        </Typography>
        {showLikes && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3" style={{ background: likesColor }} />
              <Typography className="text-white" variant="text-caption">
                Likes
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalLikes')?.value
              ).format('0.[0]a')}
            </Typography>
          </div>
        )}
        {showSurveys && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3" style={{ background: surveysColor }} />
              <Typography className="text-white" variant="text-caption">
                Survey responses
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalSurveyResponses')?.value
              ).format('0.[0]a')}
            </Typography>
          </div>
        )}
        {showQuestions && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3" style={{ background: questionsColor }} />
              <Typography className="text-white" variant="text-caption">
                Questions
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalQuestions')?.value
              ).format('0.[0]a')}
            </Typography>
          </div>
        )}
        {showViews && hasViews && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-2 w-3" style={{ background: viewsColor }} />
              <Typography className="text-white" variant="text-caption">
                Views
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(views).format('0.[0]a')}
            </Typography>
          </div>
        )}
        {showComms && (
          <>
            {payload
              .find((p) =>
                ['commsWithSummary', 'commsWithoutSummary'].includes(
                  p.dataKey as string
                )
              )
              ?.payload?.announcements.map(
                (announcement: Announcement, index: number) => (
                  <div key={`${announcement.id}-${index}`}>
                    <Link
                      className="group inline"
                      href={routes.engagement.interactiveMedia.announcement.href(
                        ticker,
                        announcement.id
                      )}
                      style={{ pointerEvents: 'all' }}
                    >
                      <span
                        className={`mr-1 inline-block h-2 w-2 rounded-full ${
                          index === 0 ? 'bg-gray-700' : 'bg-gray-900'
                        }`}
                      />
                      <Typography
                        className="inline text-white group-hover:cursor-pointer group-hover:underline"
                        variant="text-hyperlink-caption"
                      >
                        {announcement.header}
                      </Typography>
                    </Link>
                  </div>
                )
              )}
          </>
        )}
      </div>
    );
  }

  return null;
};

// This function is a modified version of recharts' internal tooltip position calculation function
const getTranslate = ({
  coordinate,
  key,
  offset,
  tooltipDimension,
  viewBox,
  viewBoxDimension,
}: {
  coordinate: {
    x: number;
    y: number;
  };
  key: 'x' | 'y';
  offset: number;
  tooltipDimension: number;
  viewBox: {
    x: number;
    y: number;
  };
  viewBoxDimension: number;
}) => {
  const restricted = coordinate[key] - tooltipDimension - offset;
  const unrestricted = coordinate[key] + offset;
  // tooltipBoundary is the coordinate of the rightmost/bottommost edge of the tooltip
  // rightmost if key === 'x'
  // bottommost if key === 'y'
  const tooltipBoundary = coordinate[key] + tooltipDimension + offset;
  // viewBoxBoundary is the coordinate of the rightmost/bottommost edge of the viewbox
  // rightmost if key === 'x'
  // bottommost if key === 'y'
  const viewBoxBoundary = viewBox[key] + viewBoxDimension;

  // If we are past the boundary, then use the restricted coordinate
  if (tooltipBoundary > viewBoxBoundary) {
    return Math.max(restricted, viewBox[key]);
  }
  // Otherwise use the unrestricted coordinate
  return Math.max(unrestricted, viewBox[key]);
};

// Custom legend below chart so we can use our own icons
const renderLegendIcon = (entry: ModifiedPayload) => {
  const { color, dataKey, type } = entry;
  switch (type) {
    case 'circle':
      return (
        <span
          className="inline-block h-[12px] w-[12px] rounded-full"
          style={{ backgroundColor: color }}
        />
      );
    case 'rect':
      return (
        <span
          className="inline-block h-[12px] w-[12px]"
          style={{ backgroundColor: color }}
        />
      );
    case 'plainline':
      return (
        <span className="inline-flex h-[12px] w-[12px] items-center">
          <span
            className="h-[2px] w-[12px]"
            style={{ backgroundColor: color }}
          />
        </span>
      );
    default:
      return (
        <svg
          fill="none"
          height="14"
          viewBox="0 0 20 20"
          width="16"
          xmlns="http://www.w3.org/2000/svg"
        >
          {dataKey === 'commsWithSummary' ? (
            <>
              <path
                d="M10 3.75002C10 3.4508 9.82215 3.18021 9.54747 3.06153C9.2728 2.94286 8.95387 2.99879 8.73598 3.20387L4.70257 7.00002H3.16724C2.85725 7.00002 2.5792 7.19074 2.46756 7.47993C2.16534 8.26287 2 9.11302 2 10C2 10.887 2.16534 11.7372 2.46756 12.5201C2.5792 12.8093 2.85725 13 3.16724 13H4.70257L8.73598 16.7962C8.95387 17.0012 9.2728 17.0572 9.54747 16.9385C9.82215 16.8198 10 16.5492 10 16.25V3.75002Z"
                fill="#111927"
              />
              <path
                d="M15.9498 5.05029C15.6569 4.7574 15.182 4.7574 14.8891 5.05029C14.5962 5.34318 14.5962 5.81806 14.8891 6.11095C17.037 8.25883 17.037 11.7412 14.8891 13.8891C14.5962 14.182 14.5962 14.6569 14.8891 14.9498C15.182 15.2427 15.6569 15.2427 15.9498 14.9498C18.6834 12.2161 18.6834 7.78396 15.9498 5.05029Z"
                fill="#111927"
              />
              <path
                d="M13.8287 7.1716C13.5358 6.8787 13.0609 6.8787 12.768 7.1716C12.4751 7.46449 12.4751 7.93936 12.768 8.23226C13.7443 9.20857 13.7443 10.7915 12.768 11.7678C12.4751 12.0607 12.4751 12.5356 12.768 12.8285C13.0609 13.1213 13.5358 13.1213 13.8287 12.8285C15.3908 11.2664 15.3908 8.73369 13.8287 7.1716Z"
                fill="#111927"
              />
            </>
          ) : (
            <path
              d="M15.9283 4.69684C18.8573 7.62577 18.8573 12.3745 15.9283 15.3034M13.7189 6.90662C15.4274 8.61516 15.4274 11.3853 13.7189 13.0938M5.625 6.87518L9.55806 2.94212C9.95179 2.5484 10.625 2.82725 10.625 3.38407V16.6163C10.625 17.1731 9.95179 17.452 9.55806 17.0582L5.625 13.1252H3.75755C3.02476 13.1252 2.33825 12.7031 2.14364 11.9967C1.96854 11.361 1.875 10.6915 1.875 10.0002C1.875 9.30886 1.96854 8.63938 2.14364 8.00371C2.33825 7.29724 3.02476 6.87518 3.75755 6.87518H5.625Z"
              stroke="#0F172A"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
            />
          )}
        </svg>
      );
  }
};

// Custom legend below chart so we can use our own icons
const CustomLegend: React.ComponentType<ModifiedLegendProps> = ({
  payload,
}) => {
  if (!payload) return null;

  return (
    <ul className="flex w-full justify-center gap-4">
      {payload.map((entry, index) => {
        return (
          <li
            key={`item-${index}`}
            className="flex items-center justify-between"
          >
            <span className="flex items-center gap-1">
              {renderLegendIcon(entry)}

              <Typography
                className="font-semibold text-gray-700"
                variant="text-label-sm"
              >
                {entry.value}
              </Typography>
            </span>
          </li>
        );
      })}
    </ul>
  );
};

const EngagementAnalyticsAnnouncementsReachAndEngagement: React.ComponentType =
  () => {
    const {
      query: { marketListingKey },
    } = useRouter();

    const containerRef = React.useRef<{ current: HTMLDivElement }>();

    const breakPoint = useBreakpoint();
    const isMobile = breakPoint ? ['sm', 'md'].includes(breakPoint) : false;

    const [tooltipMeasurements, setTooltipMeasurements] = useState({
      height: 0,
      width: 0,
    });

    // tooltipPosition is a set of coordinates relative to the viewbox
    const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

    const customTooltip = document.getElementById('custom-tooltip');

    React.useEffect(() => {
      const resizeObserver = new ResizeObserver((entries) => {
        if (entries.length > 0) {
          if (
            entries[0].contentRect &&
            entries[0].contentRect.height &&
            entries[0].contentRect.width
          ) {
            if (entries[0].contentRect.height > tooltipMeasurements.height) {
              // In some cases our resize will occur AFTER the new position of the tooltip is calculated
              // If this happens, we can potentially be using the old height to calculate position, leading to an overflow
              // if the new height is larger
              // So we do a manual update if the new height is greater
              setTooltipPosition(({ x, y }) => ({
                x: x,
                y: Math.max(
                  y -
                    (entries[0].contentRect.height -
                      tooltipMeasurements.height),
                  0
                ),
              }));
            }
            setTooltipMeasurements({
              height: entries[0].contentRect.height,
              width: entries[0].contentRect.width,
            });
          }
        }
      });

      if (customTooltip) {
        resizeObserver.observe(customTooltip);
      }

      const unobserve = () => {
        if (customTooltip) {
          resizeObserver.unobserve(customTooltip);
        }
      };
      return unobserve;
    }, [customTooltip, tooltipMeasurements]);

    const maybeUpdateTooltipPosition: CategoricalChartFunc = (props) => {
      // This provides the rectangle dimensions of the viewbox of the chart
      // We need this because recharts doesn't provide the width/height of the viewbox etc
      // We call this to get the height and width of the viewbox
      const viewBoxBoundingClientRect =
        containerRef?.current?.current.getBoundingClientRect();

      // chartX and chartY are coordinates relative to the viewbox
      const { activePayload, chartX, chartY } = props;

      const xCoordinate = getTranslate({
        coordinate: {
          x: chartX as number,
          y: chartY as number,
        },
        key: 'x',
        offset: -tooltipMeasurements.width / 4,
        tooltipDimension: tooltipMeasurements.width,
        viewBox: {
          x: 0,
          y: 0,
        },
        viewBoxDimension: viewBoxBoundingClientRect?.width as number,
      });

      const yCoordinate = getTranslate({
        coordinate: {
          x: chartX as number,
          y: chartY as number,
        },
        key: 'y',
        offset: 0,
        tooltipDimension: tooltipMeasurements.height,
        viewBox: {
          x: 0,
          y: 0,
        },
        viewBoxDimension: viewBoxBoundingClientRect?.height as number,
      });

      // If the current payload has an announcement or update, don't change y position as we want to be able to click the announcement/update
      if (activePayload?.[0]?.payload?.announcements?.[0]) {
        setTooltipPosition(({ y }) => ({ x: xCoordinate as number, y: y }));
      } else if (
        Math.abs((xCoordinate as number) - tooltipPosition.x) >
        newTooltipThreshold
      ) {
        // Otherwise, if the distance between new xCoordinate and the old xCoordinate position (tooltipPosition.x) is larger thann
        // the threshold, we want to update x and y
        if (yCoordinate) {
          setTooltipPosition({
            x: xCoordinate as number,
            y: yCoordinate as number,
          });
        }
      }
    };

    const {
      dateRangeText,
      engagementAnalyticsAnnouncementsData,
      error,
      loading,
    } = useEngagementAnalyticsAnnouncementsContext();

    const chartData = useMemo(() => {
      if (
        engagementAnalyticsAnnouncementsData?.announcementReachAndEngagement
      ) {
        // Make comms dot float on the top of chart
        const max =
          engagementAnalyticsAnnouncementsData.announcementReachAndEngagement.reduce(
            (prev, curr) => {
              const total: number =
                curr.totalLikes +
                curr.totalSurveyResponses +
                curr.totalQuestions;

              return Math.ceil(Math.max(prev, total * 1.1) / 10) * 10;
            },
            0
          );

        return engagementAnalyticsAnnouncementsData.announcementReachAndEngagement.map(
          (item) => {
            const { announcements } = item;
            return {
              ...item,
              commsWithSummary:
                announcements.length && announcements.some((a) => !!a.summary)
                  ? max + 2
                  : null,
              commsWithoutSummary:
                announcements.length && !announcements.some((a) => !!a.summary)
                  ? max + 4
                  : null,
            };
          }
        );
      }
      return [];
    }, [engagementAnalyticsAnnouncementsData?.announcementReachAndEngagement]);

    const [showLikes, toggleShowLikes] = useState(true);
    const [showSurveys, toggleShowTotalSurveys] = useState(true);
    const [showQuestions, toggleShowQuestions] = useState(true);
    const [showComms, toggleComms] = useState(true);
    const [showViews, toggleShowViews] = useState(true);

    function renderChart() {
      if (loading) {
        return <EngagementAnalyticsLoading />;
      }

      if (error || !chartData.length) {
        return <EngagementAnalyticsEmpty />;
      }

      return (
        <div className="h-[575px] w-full p-6">
          {chartData.length ? (
            <div className="flex flex-col flex-wrap gap-4 font-medium md:flex-row">
              <Checkbox
                checked={showViews ? 'yes' : 'no'}
                label={{ title: 'Views' }}
                size="sm"
                onClick={() => toggleShowViews(!showViews)}
              />
              <Checkbox
                checked={showQuestions ? 'yes' : 'no'}
                label={{ title: 'Questions' }}
                size="sm"
                onClick={() => toggleShowQuestions(!showQuestions)}
              />
              <Checkbox
                checked={showLikes ? 'yes' : 'no'}
                label={{ title: 'Likes' }}
                size="sm"
                onClick={() => toggleShowLikes(!showLikes)}
              />
              <Checkbox
                checked={showSurveys ? 'yes' : 'no'}
                label={{ title: 'Survey responses' }}
                size="sm"
                onClick={() => toggleShowTotalSurveys(!showSurveys)}
              />

              <Checkbox
                checked={showComms ? 'yes' : 'no'}
                label={{ title: 'Announcements' }}
                size="sm"
                onClick={() => toggleComms(!showComms)}
              />
            </div>
          ) : null}
          <div className="w-full overflow-auto lg:overflow-visible">
            <ResponsiveContainer
              ref={containerRef}
              debounce={100}
              height={isMobile ? 400 : 500}
              minWidth={580}
            >
              <ComposedChart
                className="border-none"
                data={chartData}
                margin={{
                  bottom: 40,
                  left: 20,
                  right: 20,
                  top: 30,
                }}
                onMouseMove={maybeUpdateTooltipPosition}
              >
                <CartesianGrid stroke="#F3F4F6" />
                <XAxis
                  axisLine={false}
                  dataKey="date"
                  interval="preserveStartEnd"
                  label={{
                    offset: -20,
                    position: 'insideBottom',
                    style: {
                      fontSize: '16px',
                      fontWeight: '600',
                    },
                    value: 'Date',
                  }}
                  tick={(props: TickProps) => {
                    const { index } = props;
                    if (index && index % 2) return <></>;
                    return <Tick {...props} fontSize="14" fontWeight="400" />;
                  }}
                  tickCount={12}
                  tickFormatter={(v) => dayjs(v).format('D MMM YY')}
                  tickLine={false}
                  tickMargin={10}
                />

                <YAxis
                  axisLine={false}
                  domain={[0, (max: number) => (max && max >= 10 ? max : 10)]}
                  label={{
                    angle: -90,
                    offset: -10,
                    position: 'insideLeft',
                    style: {
                      fontSize: '16px',
                      fontWeight: '600',
                      textAnchor: 'middle',
                    },
                    value: 'Engagement',
                  }}
                  tick={<Tick fontSize="14" fontWeight="400" />}
                  tickCount={6}
                  tickFormatter={(v) => numeral(v).format('0,0')}
                  tickLine={false}
                  tickMargin={10}
                  yAxisId="totalAudiences"
                />

                {showViews ? (
                  <YAxis
                    axisLine={{
                      stroke: '#F3F4F6',
                    }}
                    color="#6C737F"
                    domain={[
                      0,
                      (max: number) => Math.ceil((max * 1.15) / 10) * 10,
                    ]}
                    label={{
                      angle: -90,
                      offset: 70,
                      position: 'insideLeft',
                      style: {
                        fontSize: '16px',
                        fontWeight: '600',
                        textAnchor: 'middle',
                      },
                      value: 'Views',
                    }}
                    orientation="right"
                    stroke="#6C737F"
                    tick={<Tick fontSize="14" fontWeight="400" />}
                    tickCount={6}
                    tickFormatter={(v) => numeral(v).format('0.[0]a')}
                    tickLine={false}
                    tickMargin={10}
                    type="number"
                    yAxisId="views"
                  />
                ) : null}

                <Legend
                  content={<CustomLegend />}
                  formatter={(value: string) => {
                    return (
                      <Typography
                        className="inline text-gray-700"
                        variant="text-label-sm"
                      >
                        {value}
                      </Typography>
                    );
                  }}
                  iconSize={12}
                  verticalAlign="bottom"
                  wrapperStyle={{ bottom: 10 }}
                />

                {showSurveys ? (
                  <Bar
                    barSize={20}
                    dataKey="totalSurveyResponses"
                    fill={surveysColor}
                    isAnimationActive={false}
                    name="Survey responses"
                    stackId="stack"
                    yAxisId="totalAudiences"
                  />
                ) : null}

                {showLikes ? (
                  <Bar
                    barSize={20}
                    dataKey="totalLikes"
                    fill={likesColor}
                    isAnimationActive={false}
                    name="Likes"
                    stackId="stack"
                    yAxisId="totalAudiences"
                  />
                ) : null}

                {showQuestions ? (
                  <Bar
                    barSize={20}
                    dataKey="totalQuestions"
                    fill={questionsColor}
                    isAnimationActive={false}
                    name="Questions"
                    stackId="stack"
                    yAxisId="totalAudiences"
                  />
                ) : null}

                {showComms ? (
                  <>
                    <Line
                      activeDot={false}
                      dataKey="commsWithSummary"
                      dot={showComms ? <CustomizedDot /> : false}
                      isAnimationActive={false}
                      legendType="none"
                      name="Announcement (with summary)"
                      stroke={commsColor}
                      strokeWidth={0}
                      yAxisId="totalAudiences"
                    />
                    <Line
                      activeDot={false}
                      dataKey="commsWithoutSummary"
                      dot={showComms ? <CustomizedDot /> : false}
                      isAnimationActive={false}
                      legendType="none"
                      name="Announcement (no summary)"
                      stroke={commsColor}
                      strokeWidth={0}
                      yAxisId="totalAudiences"
                    />
                  </>
                ) : null}

                {showViews ? (
                  <Line
                    connectNulls
                    dataKey="totalViews"
                    dot={false}
                    isAnimationActive={false}
                    legendType="plainline"
                    name="Views"
                    stroke={viewsColor}
                    strokeWidth={2}
                    type="monotone"
                    yAxisId="views"
                  />
                ) : null}

                <ChartTooltip
                  content={
                    <CustomTooltip
                      showComms={showComms}
                      showLikes={showLikes}
                      showQuestions={showQuestions}
                      showSurveys={showSurveys}
                      showViews={showViews}
                      ticker={marketListingKey as string}
                    />
                  }
                  position={{
                    x: tooltipPosition.x,
                    y: tooltipPosition.y,
                  }}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </div>
      );
    }

    return (
      <EngagementAnalyticsComponentWrapper
        cta={
          <Button
            href={routes.engagement.interactiveMedia.announcements.href(
              marketListingKey as string
            )}
            variant="secondary-gray"
          >
            View all announcements
          </Button>
        }
        dateRangeText={dateRangeText.chart}
        error={!!error}
        loading={!!loading}
        subtitle="See how your investors are engaging with your announcements"
        subtitleLearnMoreUrl={
          INTERCOM_ARTICLES.engagementAnalytics.announcements
        }
        title="Announcement reach and engagement"
      >
        {renderChart()}
      </EngagementAnalyticsComponentWrapper>
    );
  };

export default EngagementAnalyticsAnnouncementsReachAndEngagement;
