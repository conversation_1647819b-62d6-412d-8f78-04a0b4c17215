// DS V2
import React from 'react';

import { Typography } from '@ds';

const EngagementAnalyticsEmpty: React.ComponentType = () => {
  return (
    <div className="flex h-[540px] flex-col items-center justify-center space-y-8">
      <div className="flex flex-col items-center space-y-5">
        <svg
          fill="none"
          height="57"
          viewBox="0 0 57 57"
          width="57"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            fill="#DAFCF2"
            height="48"
            rx="24"
            width="48"
            x="4.66504"
            y="4.5"
          />
          <path
            d="M23.415 19.5V21.75M33.915 19.5V21.75M19.665 35.25V24C19.665 22.7574 20.6724 21.75 21.915 21.75H35.415C36.6577 21.75 37.665 22.7574 37.665 24V35.25M19.665 35.25C19.665 36.4926 20.6724 37.5 21.915 37.5H35.415C36.6577 37.5 37.665 36.4926 37.665 35.25M19.665 35.25V27.75C19.665 26.5074 20.6724 25.5 21.915 25.5H35.415C36.6577 25.5 37.665 26.5074 37.665 27.75V35.25"
            stroke="#077656"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="1.5"
          />
          <rect
            height="48"
            rx="24"
            stroke="#F5FFFC"
            strokeWidth="8"
            width="48"
            x="4.66504"
            y="4.5"
          />
        </svg>
        <Typography
          className="px-4 text-center text-gray-500"
          variant="text-body-md"
        >
          No data available for your selected date range
        </Typography>
      </div>
    </div>
  );
};

export default EngagementAnalyticsEmpty;
