import React, { useMemo, useState } from 'react';
import { Checkbox, Tick, Typography, Tooltip } from '@ds';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/router';
import numeral from 'numeral';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip as ChartTooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import { CategoricalChartFunc } from 'recharts/types/chart/generateCategoricalChart';
import EngagementAnalyticsEmpty from '@/components/analysis/engagement/empty';
import EngagementAnalyticsLoading from '@/components/analysis/engagement/loading';
import { useEngagementAnalyticsOverviewContext } from '@/components/analysis/engagement/overview-context';
import PermissionWrapper from '@/components/layouts/permission-wrapper';
import useBreakpoint from '@/components/pdf-viewer/use-breakpoint';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { usePermissions, Permissions } from '@/hooks/use-permission';
import { INTERCOM_ARTICLES } from '@/utils/intercom-articles';
import routes from '@/utils/routes';

const uniqueVisitorsColor = '#B9E6FE';
const leadsColor = '#36BFFA';
const convertedShareholdersColor = '#026AA2';
const conversionRateColor = '#111927';
const commsColor = '#384250';
const newTooltipThreshold = 150;

type Announcement = {
  announcementId: string;
  companyProfileId: string;
  date: string;
  header: string;
};

type Update = {
  companyProfileId: string;
  date: string;
  title: string;
  updateId: string;
};
type Campaign = {
  campaignId: string;
  date: string;
  name: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CommsDot = (props: any) => {
  const { cx, cy, payload } = props;
  if (payload?.comms) {
    return (
      <circle
        cx={cx}
        cy={cy}
        fill={commsColor}
        r={4}
        stroke="black"
        strokeWidth="1"
      />
    );
  }
  return null;
};

const CustomTooltip = ({
  active,
  label,
  payload,
  showComms,
  showConversionRate,
  showLeads,
  showNominatedShareholders,
  showShareholders,
  showVisitors,
  ticker,
}: // https://github.com/recharts/recharts/issues/2796
// eslint-disable-next-line @typescript-eslint/no-explicit-any
TooltipProps<any, any> & {
  showComms: boolean;
  showConversionRate: boolean;
  showLeads: boolean;
  showNominatedShareholders: boolean;
  showShareholders: boolean;
  showVisitors: boolean;
  ticker: string;
}) => {
  if (active && payload && payload.length) {
    return (
      <div
        className="w-[240px] space-y-1 rounded-lg bg-gray-900 p-4"
        id="custom-tooltip"
      >
        <Typography className="text-white" variant="text-caption-bold">
          {`${dayjs(label).format('D MMM YY')}`}
        </Typography>
        {showVisitors && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-sky-200" />
              <Typography className="text-white" variant="text-caption">
                Unique visitors
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalUniqueVisitors')?.value
              ).format('0,0[.][00]')}
            </Typography>
          </div>
        )}
        {showLeads && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-sky-400" />
              <Typography className="text-white" variant="text-caption">
                Leads
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalLeads')?.value
              ).format('0,0[.][00]')}
            </Typography>
          </div>
        )}
        {showShareholders && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-sky-700" />
              <Typography className="text-white" variant="text-caption">
                Converted shareholders
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalConvertedShareholders')
                  ?.value
              ).format('0,0[.][00]')}
            </Typography>
          </div>
        )}
        {showNominatedShareholders && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-sky-700" />
              <Typography className="text-white" variant="text-caption">
                Nominated shareholders
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalNominatedShareholders')
                  ?.value
              ).format('0,0[.][00]')}
            </Typography>
          </div>
        )}
        {showConversionRate && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-2 w-3 bg-gray-900" />
              <Typography className="text-white" variant="text-caption">
                Lead conversion rate
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'conversionRateSma')?.value
              ).format('0,0[.][00]%')}
            </Typography>
          </div>
        )}
        {showComms && (
          <>
            {payload
              .find((p) => p.dataKey === 'comms')
              ?.payload?.announcements.map(
                (announcement: Announcement, index: number) => (
                  <div key={announcement.announcementId}>
                    <Link
                      className="group inline"
                      href={routes.engagement.interactiveMedia.announcement.href(
                        ticker,
                        announcement.announcementId
                      )}
                      style={{ pointerEvents: 'all' }}
                    >
                      <span
                        className={`mr-1 inline-block h-2 w-2 rounded-full ${
                          index === 0 ? 'bg-gray-700' : 'bg-gray-900'
                        }`}
                      />
                      <Typography
                        className="inline text-white group-hover:cursor-pointer group-hover:underline"
                        variant="text-hyperlink-caption"
                      >
                        {announcement.header}
                      </Typography>
                    </Link>
                  </div>
                )
              )}
            {payload
              .find((p) => p.dataKey === 'comms')
              ?.payload?.updates.map((update: Update, index: number) => (
                <div key={update.updateId}>
                  <Link
                    className="group inline"
                    href={routes.engagement.interactiveMedia.update.href(
                      ticker,
                      update.updateId
                    )}
                    style={{ pointerEvents: 'all' }}
                  >
                    <Typography
                      className="inline text-white group-hover:cursor-pointer group-hover:underline"
                      variant="text-hyperlink-caption"
                    >
                      <span
                        className={`mr-1 inline-block h-2 w-2 rounded-full ${
                          index === 0 ? 'bg-gray-700' : 'bg-gray-900'
                        }`}
                      />
                      {update.title}
                    </Typography>
                  </Link>
                </div>
              ))}
            {payload
              .find((p) => p.dataKey === 'comms')
              ?.payload?.campaigns.map((campaign: Campaign, index: number) => (
                <div key={campaign.campaignId}>
                  <Link
                    className="group inline"
                    href={routes.engagement.campaigns.campaign.href(
                      ticker,
                      campaign.campaignId
                    )}
                    style={{ pointerEvents: 'all' }}
                  >
                    <Typography
                      className="inline text-white group-hover:cursor-pointer group-hover:underline"
                      variant="text-hyperlink-caption"
                    >
                      <span
                        className={`mr-1 inline-block h-2 w-2 rounded-full ${
                          index === 0 ? 'bg-gray-700' : 'bg-gray-900'
                        }`}
                      />
                      {campaign.name}
                    </Typography>
                  </Link>
                </div>
              ))}
          </>
        )}
      </div>
    );
  }

  return null;
};

// This function is a modified version of recharts' internal tooltip position calculation function
const getTranslate = ({
  coordinate,
  key,
  offset,
  tooltipDimension,
  viewBox,
  viewBoxDimension,
}: {
  coordinate: {
    x: number;
    y: number;
  };
  key: 'x' | 'y';
  offset: number;
  tooltipDimension: number;
  viewBox: {
    x: number;
    y: number;
  };
  viewBoxDimension: number;
}) => {
  const restricted = coordinate[key] - tooltipDimension - offset;
  const unrestricted = coordinate[key] + offset;
  // tooltipBoundary is the coordinate of the rightmost/bottommost edge of the tooltip
  // rightmost if key === 'x'
  // bottommost if key === 'y'
  const tooltipBoundary = coordinate[key] + tooltipDimension + offset;
  // viewBoxBoundary is the coordinate of the rightmost/bottommost edge of the viewbox
  // rightmost if key === 'x'
  // bottommost if key === 'y'
  const viewBoxBoundary = viewBox[key] + viewBoxDimension;

  // If we are past the boundary, then use the restricted coordinate
  if (tooltipBoundary > viewBoxBoundary) {
    return Math.max(restricted, viewBox[key]);
  }
  // Otherwise use the unrestricted coordinate
  return Math.max(unrestricted, viewBox[key]);
};

const EngagementAnalyticsAudienceBreakdown: React.ComponentType = () => {
  const {
    query: { marketListingKey },
  } = useRouter();
  const { isPremium } = useCurrentCompanyProfileUser();

  const containerRef = React.useRef<{ current: HTMLDivElement }>();

  const breakPoint = useBreakpoint();
  const isMobile = breakPoint ? ['sm', 'md'].includes(breakPoint) : false;

  const [tooltipMeasurements, setTooltipMeasurements] = useState({
    height: 0,
    width: 0,
  });

  const customTooltip = document.getElementById('custom-tooltip');

  React.useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      if (entries.length > 0) {
        if (
          entries[0].contentRect &&
          entries[0].contentRect.height &&
          entries[0].contentRect.width
        ) {
          if (entries[0].contentRect.height > tooltipMeasurements.height) {
            // In some cases our resize will occur AFTER the new position of the tooltip is calculated
            // If this happens, we can potentially be using the old height to calculate position, leading to an overflow
            // if the new height is larger
            // So we do a manual update if the new height is greater
            setTooltipPosition(({ x, y }) => ({
              x: x,
              y: Math.max(
                y -
                  (entries[0].contentRect.height - tooltipMeasurements.height),
                0
              ),
            }));
          }
          setTooltipMeasurements({
            height: entries[0].contentRect.height,
            width: entries[0].contentRect.width,
          });
        }
      }
    });

    if (customTooltip) {
      resizeObserver.observe(customTooltip);
    }

    const unobserve = () => {
      if (customTooltip) {
        resizeObserver.unobserve(customTooltip);
      }
    };
    return unobserve;
  }, [customTooltip, tooltipMeasurements]);

  // tooltipPosition is a set of coordinates relative to the viewbox
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  const maybeUpdateTooltipPosition: CategoricalChartFunc = (props) => {
    // This provides the rectangle dimensions of the viewbox of the chart
    // We need this because recharts doesn't provide the width/height of the viewbox etc
    // We call this to get the height and width of the viewbox
    const viewBoxBoundingClientRect =
      containerRef?.current?.current.getBoundingClientRect();

    // chartX and chartY are coordinates relative to the viewbox
    const { activePayload, chartX, chartY } = props;

    const xCoordinate = getTranslate({
      coordinate: {
        x: chartX as number,
        y: chartY as number,
      },
      key: 'x',
      offset: -tooltipMeasurements.width / 4,
      tooltipDimension: tooltipMeasurements.width,
      viewBox: {
        x: 0,
        y: 0,
      },
      viewBoxDimension: viewBoxBoundingClientRect?.width as number,
    });

    const yCoordinate = getTranslate({
      coordinate: {
        x: chartX as number,
        y: chartY as number,
      },
      key: 'y',
      offset: 0,
      tooltipDimension: tooltipMeasurements.height,
      viewBox: {
        x: 0,
        y: 0,
      },
      viewBoxDimension: viewBoxBoundingClientRect?.height as number,
    });

    // If the current payload has an announcement or update, don't change y position as we want to be able to click the announcement/update
    if (
      activePayload?.[0]?.payload?.announcements?.[0] ||
      activePayload?.[0]?.payload?.updates?.[0]
    ) {
      setTooltipPosition(({ y }) => ({ x: xCoordinate as number, y: y }));
    } else if (
      Math.abs((xCoordinate as number) - tooltipPosition.x) >
      newTooltipThreshold
    ) {
      // Otherwise, if the distance between new xCoordinate and the old xCoordinate position (tooltipPosition.x) is larger thann
      // the threshold, we want to update x and y
      if (yCoordinate) {
        setTooltipPosition({
          x: xCoordinate as number,
          y: yCoordinate as number,
        });
      }
    }
  };

  const { dateRangeText, engagementAnalyticsOverviewData, error, loading } =
    useEngagementAnalyticsOverviewContext();

  const chartData = useMemo(() => {
    if (engagementAnalyticsOverviewData?.engagementAnalyticsOverview) {
      // Make comms dot float on the top of chart
      const max =
        engagementAnalyticsOverviewData.engagementAnalyticsOverview.audiencesBreakdown.reduce(
          (prev, curr) => {
            const total = isPremium
              ? curr.totalConvertedShareholders
              : curr.totalNominatedShareholders +
                curr.totalLeads +
                curr.totalUniqueVisitors;
            return Math.max(prev, total);
          },
          0
        );

      return engagementAnalyticsOverviewData.engagementAnalyticsOverview.audiencesBreakdown.map(
        (item) => {
          return {
            ...item,
            comms:
              item.announcements.length ||
              item.updates.length ||
              item.campaigns.length
                ? max + max * 0.1
                : null,
          };
        }
      );
    }
    return [];
  }, [engagementAnalyticsOverviewData?.engagementAnalyticsOverview, isPremium]);

  const [showVisitors, toggleVisitors] = useState(true);
  const [showLeads, toggleLeads] = useState(true);
  const [showShareholders, toggleShareholders] = useState(isPremium);
  const [showNominatedShareholders, toggleNominatedShareholders] = useState(
    !isPremium
  );

  const hasSomeCommsPermissions = usePermissions([
    Permissions.commsEmailsAdmin,
    Permissions.interactionsMediaAnnouncementsAdmin,
    Permissions.interactionsMediaAnnouncementsEditor,
    Permissions.interactionsMediaUpdatesAdmin,
  ]);

  const [showComms, toggleComms] = useState(
    hasSomeCommsPermissions ? true : false
  );
  const [showConversionRate, toggleConversionRate] = useState(isPremium);

  function renderChart() {
    if (loading) {
      return <EngagementAnalyticsLoading />;
    }

    if (error || !chartData.length) {
      return <EngagementAnalyticsEmpty />;
    }

    return (
      <div className="h-[575px] w-full p-6">
        {chartData.length ? (
          <div className="flex flex-col flex-wrap gap-4 font-medium md:flex-row">
            <div className="flex items-center gap-1.5">
              <Checkbox
                checked={showVisitors ? 'yes' : 'no'}
                label={{ description: '', title: 'Visitors' }}
                size="sm"
                onClick={() => toggleVisitors(!showVisitors)}
              />

              <Tooltip
                content="Unique user and device visits to your investor hub"
                iconClassName="text-gray-400"
                id="visitors-tooltip"
              />
            </div>

            <div className="flex items-center gap-1.5">
              <Checkbox
                checked={showLeads ? 'yes' : 'no'}
                label={{ description: '', title: 'Leads' }}
                size="sm"
                onClick={() => toggleLeads(!showLeads)}
              />

              <Tooltip
                content="Potential shareholders from your investor hub and imported contacts"
                iconClassName="text-gray-400"
                id="potential-shareholders-tooltip"
              />
            </div>
            {isPremium ? (
              <Checkbox
                checked={showShareholders ? 'yes' : 'no'}
                label={{ description: '', title: 'Shareholders' }}
                size="sm"
                onClick={() => toggleShareholders(!showShareholders)}
              />
            ) : (
              <Checkbox
                checked={showNominatedShareholders ? 'yes' : 'no'}
                label={{ description: '', title: 'Nominated shareholders' }}
                size="sm"
                onClick={() =>
                  toggleNominatedShareholders(!showNominatedShareholders)
                }
              />
            )}
            <PermissionWrapper
              name={[
                Permissions.commsEmailsAdmin,
                Permissions.interactionsMediaAnnouncementsAdmin,
                Permissions.interactionsMediaAnnouncementsEditor,
                Permissions.interactionsMediaUpdatesAdmin,
              ]}
            >
              <div className="flex items-center gap-1.5">
                <Checkbox
                  checked={showComms ? 'yes' : 'no'}
                  label={{ description: '', title: 'Investor comms' }}
                  size="sm"
                  onClick={() => toggleComms(!showComms)}
                />

                <Tooltip
                  content="Release of announcements, updates, and any type of email, mail or distribution campaign"
                  iconClassName="text-gray-400"
                  id="announcement-release-tooltip"
                />
              </div>
            </PermissionWrapper>
            <div className="flex items-center gap-1.5">
              {isPremium ? (
                <Checkbox
                  checked={showConversionRate ? 'yes' : 'no'}
                  label={{ description: '', title: 'Lead conversion rate' }}
                  size="sm"
                  onClick={() => toggleConversionRate(!showConversionRate)}
                />
              ) : null}
            </div>
          </div>
        ) : null}
        <div className="w-full overflow-auto lg:overflow-visible">
          <ResponsiveContainer
            ref={containerRef}
            height={isMobile ? 400 : 500}
            minWidth={580}
          >
            <ComposedChart
              className="border-none"
              data={chartData}
              margin={{
                bottom: 40,
                left: 20,
                right: 20,
                top: 30,
              }}
              onMouseMove={maybeUpdateTooltipPosition}
            >
              <CartesianGrid stroke="#F3F4F6" />
              <XAxis
                axisLine={false}
                dataKey="date"
                label={{
                  offset: -20,
                  position: 'insideBottom',
                  style: {
                    fontSize: '16px',
                    fontWeight: '600',
                  },
                  value: 'Date',
                }}
                minTickGap={20}
                scale={showConversionRate ? 'point' : 'band'}
                tick={
                  <Tick hideLast dy={'0px'} fontSize="14" fontWeight="400" />
                }
                tickCount={5}
                tickFormatter={(v) => dayjs(v).format('D MMM YY')}
                tickLine={false}
                tickMargin={15}
              />

              <YAxis
                axisLine={false}
                domain={[0, (max: number) => (max ? max * 1.1 : 500)]}
                label={{
                  angle: -90,
                  offset: -10,
                  position: 'insideLeft',
                  style: {
                    fontSize: '16px',
                    fontWeight: '600',
                    textAnchor: 'middle',
                  },
                  value: 'Audience (No.)',
                }}
                tick={<Tick fontSize="14" />}
                tickFormatter={(v) => numeral(v).format('0,0')}
                tickLine={false}
                tickMargin={10}
                yAxisId="totalAudiences"
              />

              {showConversionRate ? (
                <YAxis
                  axisLine={{
                    stroke: 'white',
                  }}
                  color="#111927"
                  domain={[
                    0,
                    (max: number) => (max ? Math.min(max * 2, 1) : 1),
                  ]}
                  label={{
                    angle: -90,
                    offset: 70,
                    position: 'insideLeft',
                    style: {
                      fontSize: '16px',
                      fontWeight: '600',
                      textAnchor: 'middle',
                    },
                    value: 'Lead conversion rate (%)',
                  }}
                  orientation="right"
                  stroke="#6C737F"
                  tick={{ stroke: '#6C737F', strokeWidth: 0.1 }}
                  tickFormatter={(v) => numeral(v).format('0,0[.]0%')}
                  tickLine={false}
                  type="number"
                  yAxisId="leadConversionRateAndComms"
                />
              ) : null}

              <Legend
                formatter={(value: string) => {
                  return (
                    <Typography
                      className="inline text-gray-700"
                      variant="text-label-sm"
                    >
                      {value}
                    </Typography>
                  );
                }}
                iconSize={12}
                verticalAlign="bottom"
                wrapperStyle={{ bottom: 10 }}
              />

              {showShareholders ? (
                <Bar
                  barSize={20}
                  dataKey="totalConvertedShareholders"
                  fill={convertedShareholdersColor}
                  isAnimationActive={false}
                  name="Shareholders"
                  stackId="stack"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {showNominatedShareholders ? (
                <Bar
                  barSize={20}
                  dataKey="totalNominatedShareholders"
                  fill={convertedShareholdersColor}
                  isAnimationActive={false}
                  name="Nominated shareholders"
                  stackId="stack"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {showLeads ? (
                <Bar
                  barSize={20}
                  dataKey="totalLeads"
                  fill={leadsColor}
                  isAnimationActive={false}
                  name="Leads"
                  stackId="stack"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {showVisitors ? (
                <Bar
                  barSize={20}
                  dataKey="totalUniqueVisitors"
                  fill={uniqueVisitorsColor}
                  isAnimationActive={false}
                  name="Visitors"
                  stackId="stack"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {/* Line is after the bar so it rendered on top of the bar chart */}
              {showComms ? (
                <Line
                  activeDot={{
                    fill: 'white',
                    r: 4,
                    stroke: commsColor,
                    strokeWidth: 2,
                  }}
                  dataKey="comms"
                  dot={showComms ? <CommsDot /> : false}
                  isAnimationActive={false}
                  legendType="circle"
                  name="Investor comms"
                  stroke={commsColor}
                  strokeWidth={0}
                  type="monotone"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {showConversionRate ? (
                <Line
                  dataKey="conversionRateSma"
                  dot={false}
                  isAnimationActive={false}
                  legendType="plainline"
                  name="Lead Conversion Rate"
                  stroke={conversionRateColor}
                  strokeWidth={2}
                  type="monotone"
                  yAxisId="leadConversionRateAndComms"
                />
              ) : null}

              <ChartTooltip
                content={
                  <CustomTooltip
                    showComms={showComms}
                    showConversionRate={showConversionRate}
                    showLeads={showLeads}
                    showNominatedShareholders={showNominatedShareholders}
                    showShareholders={showShareholders}
                    showVisitors={showVisitors}
                    ticker={marketListingKey as string}
                  />
                }
                position={{
                  x: tooltipPosition.x,
                  y: tooltipPosition.y,
                }}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 rounded-lg border border-gray-200 bg-white">
      <div className="border-b border-b-gray-200 px-6 py-5">
        <Typography className="text-gray-900" variant="text-heading-sm">
          Audience breakdown{' '}
          <span className="lowercase">{dateRangeText.chart}</span>
        </Typography>
        <div className="flex gap-1">
          <Typography className="text-gray-500" variant="text-button-sm">
            Compare audience growth with your investor communications and
            conversion rate.{' '}
            <a
              className="text-amplify-green-900"
              href={INTERCOM_ARTICLES.engagementAnalytics.overview}
              rel="noopener noreferrer"
              target="_blank"
            >
              Learn more
            </a>
          </Typography>
        </div>
      </div>
      {renderChart()}
    </div>
  );
};

export default EngagementAnalyticsAudienceBreakdown;
