import React from 'react';
import { But<PERSON>, Typo<PERSON>, UpvoteIcon } from '@ds';
import {
  ChatIcon,
  ChevronRightIcon,
  ClipboardIcon,
} from '@heroicons/react/outline';
import clsx from 'clsx';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { EngagementAnalyticsInvestorHubQuery } from '@/apollo/generated';
import EngagementAnalyticsComponentWrapper from '@/components/analysis/engagement/engagement-analytics-component-wrapper';
import { useEngagementAnalyticsInvestorHubContext } from '@/components/analysis/engagement/investorhub-context';
import { getUserFullName } from '@/components/comms/settings/email/helpers';
import ShareholdingBadges from '@/components/contacts/profile/shareholdings/shareholding-badges';
import ShareholderStatusBadge from '@/components/investors/general/shareholder-status-badge';
import PermissionWrapper from '@/components/layouts/permission-wrapper';
import { Permissions } from '@/hooks/use-permission';
import routes from '@/utils/routes';

interface ContactBadgesProps {
  contact: NonNullable<
    NonNullable<
      EngagementAnalyticsInvestorHubQuery['investorHubMostEngagedInvestors']
    >[0]['investorUser']
  >['contact'];
}

const ContactBadges: React.FC<ContactBadgesProps> = ({ contact }) => {
  if (!contact) return null;

  const { shareholderStatus, shareholdings } = contact;
  return (
    <PermissionWrapper name={Permissions.registersShareholdingsAdmin}>
      <div className="flex items-center gap-2">
        <ShareholderStatusBadge shareholderStatus={shareholderStatus} />
        {shareholdings.length ? (
          <ShareholdingBadges showTooltips shareholding={shareholdings[0]} />
        ) : null}
      </div>
    </PermissionWrapper>
  );
};

interface InvestorHubMostEngagedInvestorProps {
  isPremium: boolean;
}

const InvestorHubMostEngagedInvestor = ({
  isPremium,
}: InvestorHubMostEngagedInvestorProps) => {
  const {
    query: { marketListingKey },
  } = useRouter();

  const { dateRangeText, engagementAnalyticsInvestorHubData, error, loading } =
    useEngagementAnalyticsInvestorHubContext();

  return (
    <EngagementAnalyticsComponentWrapper
      dateRangeText={dateRangeText.chart}
      error={!!error}
      loading={!!loading}
      subtitle="Learn more about the top 5 most active investors on your investor hub"
      title="Your most engaged investors"
      tooltipDescription="Combined count of engagement activity including: questions, likes, and survey responses"
    >
      {engagementAnalyticsInvestorHubData?.investorHubMostEngagedInvestors.map(
        (investor, index) => {
          return investor.investorUser?.contact?.id ? (
            <Link
              key={index}
              href={
                routes.investors.search.contacts.contact.href(
                  marketListingKey as string,
                  investor.investorUser?.contact?.id
                ) + '#activity-log'
              }
            >
              <div
                key={investor.investorUser?.contact?.id}
                className={clsx(
                  'grid grid-cols-9 gap-2 py-4 hover:bg-gray-50',
                  index !==
                    engagementAnalyticsInvestorHubData
                      ?.investorHubMostEngagedInvestors.length -
                      1 && 'border-b border-b-gray-200'
                )}
              >
                <Typography
                  className="col-span-1 place-self-center"
                  variant="text-heading-md"
                >
                  {index + 1}
                </Typography>
                <div className="col-span-6">
                  <div className="mb-1 flex flex-wrap items-center gap-1">
                    <Typography className="break-all" variant="text-label-sm">
                      {getUserFullName(
                        investor.investorUser.contact.firstName,
                        investor.investorUser.contact.lastName
                      )}
                      {investor.investorUser.username
                        ? ` (${investor.investorUser.username})`
                        : null}
                    </Typography>
                    {investor.investorUser.contact ? (
                      <ContactBadges contact={investor.investorUser.contact} />
                    ) : null}
                  </div>
                  <div className="flex flex-col gap-2 md:flex-row">
                    <div className="flex items-center gap-1 ">
                      <ChatIcon className="h-4 w-4 text-gray-500" />
                      <Typography
                        className="text-gray-500"
                        variant="text-caption"
                      >
                        {investor.questions} questions
                      </Typography>
                    </div>
                    <div className="flex items-center gap-1">
                      <UpvoteIcon className="h-4 w-4 text-gray-500" />
                      <Typography
                        className="text-gray-500"
                        variant="text-caption"
                      >
                        {investor.likes} likes
                      </Typography>
                    </div>
                    <div className="flex items-center gap-1">
                      <ClipboardIcon className="h-4 w-4 text-gray-500" />
                      <Typography
                        className="text-gray-500"
                        variant="text-caption"
                      >
                        {investor.surveyResponses} survey responses
                      </Typography>
                    </div>
                  </div>
                </div>
                <div className="col-span-9 place-self-center p-2 md:col-span-2 md:p-0">
                  <Button TrailingIcon={ChevronRightIcon} variant="link-gray">
                    View activity
                  </Button>
                </div>
              </div>
            </Link>
          ) : null;
        }
      )}
    </EngagementAnalyticsComponentWrapper>
  );
};

export default InvestorHubMostEngagedInvestor;
