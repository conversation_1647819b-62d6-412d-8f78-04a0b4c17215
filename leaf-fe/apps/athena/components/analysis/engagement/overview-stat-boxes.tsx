import React from 'react';
import { StatCard } from '@ds';
import { CheckCircleIcon, UsersIcon, UserIcon } from '@heroicons/react/solid';
import { useRouter } from 'next/router';
import { useEngagementAnalyticsOverviewContext } from '@/components/analysis/engagement/overview-context';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import routes from '@/utils/routes';

const EngagementAnalyticsOverviewStatBoxes: React.ComponentType = () => {
  const router = useRouter();
  const { dateRangeText, engagementAnalyticsOverviewData, error, loading } =
    useEngagementAnalyticsOverviewContext();
  const { isPremium } = useCurrentCompanyProfileUser();

  return (
    <div className="w-full gap-6 lg:flex">
      {isPremium ? (
        <div className="flex w-full">
          <StatCard
            badgeNum={
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .convertedShareholdersDifference ?? 0
            }
            error={!!error}
            icon={
              <div className="h-12 w-12 rounded-full bg-violet-50 p-3.5">
                <CheckCircleIcon className="h-5 w-5 text-violet-700" />
              </div>
            }
            loading={loading}
            statNum={
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .totalConvertedShareholders ?? 0
            }
            timePeriodText={dateRangeText.statBox}
            title="Converted shareholders"
            tooltipText="Investor hub leads who have bought shares or linked their shareholdings"
            onClickAction={() =>
              router.push({
                pathname: routes.investors.search.href(
                  router.query.marketListingKey as string
                ),
                query: {
                  shareholder_status: 'shareholder',
                },
              })
            }
          />
        </div>
      ) : null}
      <div className="flex w-full">
        <StatCard
          badgeNum={
            engagementAnalyticsOverviewData?.engagementAnalyticsOverview
              .leadsDifference ?? 0
          }
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-fuchsia-50 p-3.5">
              <UsersIcon className="h-5 w-5 text-fuchsia-700" />
            </div>
          }
          loading={loading}
          statNum={
            engagementAnalyticsOverviewData?.engagementAnalyticsOverview
              .totalLeads ?? 0
          }
          timePeriodText={dateRangeText.statBox}
          title="Leads"
          tooltipText="Potential shareholders from your investor hub and imported contacts"
          onClickAction={() =>
            router.push({
              pathname: routes.investors.search.href(
                router.query.marketListingKey as string
              ),
              query: {
                lead_status: 'investor-lead',
              },
            })
          }
        />
      </div>
      <div className="flex w-full">
        <StatCard
          badgeNum={
            engagementAnalyticsOverviewData?.engagementAnalyticsOverview
              .uniqueVisitorsDifference ?? 0
          }
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-cyan-50 p-3.5">
              <UserIcon className="h-5 w-5 text-cyan-700" />
            </div>
          }
          loading={loading}
          statNum={
            engagementAnalyticsOverviewData?.engagementAnalyticsOverview
              .totalUniqueVisitors ?? 0
          }
          timePeriodText={dateRangeText.statBox}
          title="Unique visitors"
          tooltipText="Unique user and device visits to your investor hub"
        />
      </div>
    </div>
  );
};

export default EngagementAnalyticsOverviewStatBoxes;
