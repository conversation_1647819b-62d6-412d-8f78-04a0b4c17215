// DS V2
import React, { useEffect, useState, useRef } from 'react';
import { DateRangePicker, buttonStyles, dropdownStyles, Typography } from '@ds';
import { Listbox } from '@headlessui/react';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
} from '@heroicons/react/outline';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useOnClickOutside } from 'usehooks-ts';

export type DateRange =
  | 'Last 7 days'
  | 'Last 30 days'
  | 'Last 90 days'
  | 'Last 180 days'
  | 'Choose a date range';
interface Props {
  disabled?: boolean;
  useContext: () => {
    endDate: Maybe<Date>;
    maxDate?: Date;
    minDate?: Date;
    queryVariables: {
      dateRange: DateRange;
      endDate: Maybe<Date>;
      startDate: Maybe<Date>;
    };
    setEndDate: (endDate: Maybe<Date>) => void;
    setQueryVariables: (
      dateRange: DateRange,
      startDate: Maybe<Date>,
      endDate: Maybe<Date>
    ) => void;
    setStartDate: (startDate: Maybe<Date>) => void;
    startDate: Maybe<Date>;
  };
  variant?: 'primary' | 'secondary-gray';
}

type option = { days: number; name: DateRange };
const options: option[] = [
  { days: 7, name: 'Last 7 days' },
  { days: 30, name: 'Last 30 days' },
  { days: 90, name: 'Last 90 days' },
  { days: 180, name: 'Last 180 days' },
  { days: 0, name: 'Choose a date range' },
];

const AnalyticsDatePicker: React.ComponentType<Props> = ({
  disabled,
  useContext,
  variant = 'primary',
}) => {
  const {
    endDate,
    maxDate,
    minDate,
    queryVariables,
    setEndDate,
    setQueryVariables,
    setStartDate,
    startDate,
  } = useContext();

  const [rangePickerOpen, setRangePickerOpen] = useState<boolean>(false);

  const [selectedOption, setSelectedOption] = useState(
    options.find((option) => option.name === queryVariables.dateRange) ||
      options[1]
  );

  const selectedText = () => {
    const startDisplay = startDate ? dayjs(startDate).format('D/M/YYYY') : '';
    const endDisplay = endDate ? dayjs(endDate).format('D/M/YYYY') : '';
    if (selectedOption.name === options[4].name && (startDate || endDate)) {
      return `${startDisplay} - ${endDisplay}`;
    }
    return selectedOption.name;
  };

  const dateMinusDays = (days: number) =>
    new Date(new Date().setDate(new Date().getDate() - days));

  useEffect(() => {
    if (selectedOption.name !== 'Choose a date range') {
      setStartDate(dateMinusDays(selectedOption.days));
      setEndDate(new Date());
    }
  }, [selectedOption, setEndDate, setStartDate]);

  const [rangePickerWasOpen, setRangePickerWasOpen] = useState<boolean>(false);

  useEffect(() => {
    if (rangePickerOpen && !rangePickerWasOpen) {
      setRangePickerWasOpen(true);
    }

    if (!rangePickerOpen && rangePickerWasOpen) {
      setRangePickerWasOpen(false);
      setQueryVariables(selectedOption.name, endDate, startDate);
    }
  }, [
    rangePickerOpen,
    setQueryVariables,
    selectedOption.name,
    startDate,
    endDate,
    rangePickerWasOpen,
  ]);

  const onChange = (option: option) => {
    setSelectedOption(option);
    if (option.name === 'Choose a date range') {
      setRangePickerOpen(true);
    } else {
      setQueryVariables(option.name, null, null);
    }
  };

  const DatePickerWrapper: React.FC = (props) => {
    const ref = useRef(null);

    const handleClickOutside = () => {
      setRangePickerOpen(false);
    };

    useOnClickOutside(ref, handleClickOutside);

    return (
      <div ref={ref}>
        <DateRangePicker
          endDate={endDate}
          maxDate={maxDate}
          minDate={minDate}
          setEndDate={setEndDate}
          setOpen={setRangePickerOpen}
          setStartDate={setStartDate}
          startDate={startDate}
          {...props}
        />
      </div>
    );
  };

  return (
    <Listbox
      as="div"
      className="relative"
      disabled={disabled}
      value={selectedOption}
      onChange={onChange}
    >
      {({ open }) => (
        <>
          <Listbox.Button
            className={clsx(
              buttonStyles['base'],
              buttonStyles['pad-sm'],
              variant == 'primary'
                ? buttonStyles['primary']
                : buttonStyles['secondary-gray'],
              buttonStyles['button-sm'],
              'w-full sm:w-auto'
            )}
          >
            {selectedText()}
            {open ? (
              <ChevronUpIcon className="h-4 w-4" />
            ) : (
              <ChevronDownIcon className="h-4 w-4" />
            )}
          </Listbox.Button>
          {(selectedOption.name !== options[4].name ||
            (open && !rangePickerOpen)) && (
            <Listbox.Options
              className="
                absolute right-0 z-50 mt-2 w-full max-w-none overflow-hidden rounded-md bg-white shadow-lg sm:w-[250px] sm:max-w-xs"
            >
              {options.map((option, index) => (
                <Listbox.Option
                  key={index}
                  className={clsx(
                    dropdownStyles['dropdown-item'],
                    option === selectedOption
                      ? dropdownStyles['dropdown-item-selected']
                      : dropdownStyles['dropdown-item-unselected']
                  )}
                  value={option}
                >
                  <Typography className="text-gray-900" variant="text-body-sm">
                    {option.name}
                  </Typography>
                  {option === selectedOption && (
                    <CheckIcon className="h-5 w-5 text-gray-900" />
                  )}
                </Listbox.Option>
              ))}
            </Listbox.Options>
          )}
          {rangePickerOpen && <DatePickerWrapper />}
        </>
      )}
    </Listbox>
  );
};

export default AnalyticsDatePicker;
