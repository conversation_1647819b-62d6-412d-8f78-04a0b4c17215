import React from 'react';
import { Badge, StatCard } from '@ds';
import { ChatIcon, EyeIcon, UserAddIcon } from '@heroicons/react/outline';
import { useRouter } from 'next/router';
import { useEngagementAnalyticsBenchmarkContext } from '@/components/analysis/engagement/benchmark/context';
import routes from '@/utils/routes';

const EngagementAnalyticsBenchmarkStatBoxes: React.ComponentType = () => {
  const router = useRouter();

  const { benchmarkData, error, loading } =
    useEngagementAnalyticsBenchmarkContext();

  return (
    <div className="w-full grid-cols-3 gap-6 space-y-4 lg:grid lg:space-y-0">
      <div className="col-span-1 flex w-full">
        <StatCard
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-green-50 p-3.5">
              <UserAddIcon className="h-5 w-5 text-green-600" />
            </div>
          }
          loading={loading}
          statBadge={
            benchmarkData?.benchmarkAnalytics.currentCompanyStats
              .signupsTopFive ? (
              <Badge color="green" size="xs">
                Top 5
              </Badge>
            ) : undefined
          }
          statNum={
            benchmarkData?.benchmarkAnalytics.currentCompanyStats
              .signupsCount ?? 0
          }
          title="Hub sign-ups"
          tooltipText="Number of new sign-ups to your hub"
          onClickAction={() =>
            router.push({
              pathname: routes.investors.search.href(
                router.query.marketListingKey as string
              ),
              query: {
                has_investor: 'linked-only',
                hub_sign_ups_days_ago: '30',
              },
            })
          }
        />
      </div>
      <div className="col-span-1 flex w-full">
        <StatCard
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-cyan-50 p-3.5">
              <EyeIcon className="h-5 w-5 text-cyan-600" />
            </div>
          }
          loading={loading}
          statBadge={
            benchmarkData?.benchmarkAnalytics.currentCompanyStats
              .viewsTopFive ? (
              <Badge color="green" size="xs">
                Top 5
              </Badge>
            ) : undefined
          }
          statNum={
            benchmarkData?.benchmarkAnalytics.currentCompanyStats.viewsCount ??
            0
          }
          title="Total views"
          tooltipText="Total page views, including repeat views from the same user"
        />
      </div>
      <div className="col-span-1 flex w-full">
        <StatCard
          error={!!error}
          icon={
            <div className="h-12 w-12 rounded-full bg-violet-50 p-3.5">
              <ChatIcon className="h-5 w-5 text-violet-600" />
            </div>
          }
          loading={loading}
          statBadge={
            benchmarkData?.benchmarkAnalytics.currentCompanyStats
              .hubActionsTopFive ? (
              <Badge color="green" size="xs">
                Top 5
              </Badge>
            ) : undefined
          }
          statNum={
            benchmarkData?.benchmarkAnalytics.currentCompanyStats
              .hubActionsCount ?? 0
          }
          title="Activity"
          tooltipText="Total number of likes, questions and surveys"
        />
      </div>
    </div>
  );
};

export default EngagementAnalyticsBenchmarkStatBoxes;
