import { FC, useCallback } from 'react';
import { Badge, Typography } from '@ds';
import numeral from 'numeral';

interface Props {
  currentCompanyValue: number;
  icon: JSX.Element;
  label: string;
  peerCompaniesValue: number;
}

const EngagementAnalyticsBenchmarkTableRow: FC<Props> = ({
  currentCompanyValue,
  icon,
  label,
  peerCompaniesValue,
}) => {
  const difference = currentCompanyValue - peerCompaniesValue;

  const renderRelativePerformance = useCallback(() => {
    if (peerCompaniesValue === 0) {
      return <Typography variant="text-body-sm">N/A</Typography>;
    }

    if (currentCompanyValue === 0) {
      return (
        <Badge color="red" size="xs">
          -100%
        </Badge>
      );
    }

    const differencePercentage = difference / peerCompaniesValue;

    return (
      <Badge color={difference >= 0 ? 'green' : 'red'} size="xs">
        {difference > 0 ? '+' : ''}
        {numeral(differencePercentage).format('0%')}
      </Badge>
    );
  }, [currentCompanyValue, difference, peerCompaniesValue]);

  return (
    <tr className="table-row border-t border-t-gray-200">
      <td className="py-3 pl-6">
        <div className="flex items-center space-x-2">
          {icon}

          <Typography variant="text-label-sm">{label}</Typography>
        </div>
      </td>

      <td className="px-3 py-4">
        <Typography className="text-right" variant="text-body-sm">
          {numeral(currentCompanyValue).format('0,0')}
        </Typography>
      </td>

      <td className="px-3 py-4">
        <Typography className="text-right" variant="text-body-sm">
          {numeral(peerCompaniesValue).format('0,0')}
        </Typography>
      </td>

      <td className="flex items-center justify-end space-x-2 py-4 pr-6">
        {renderRelativePerformance()}
      </td>
    </tr>
  );
};

export default EngagementAnalyticsBenchmarkTableRow;
