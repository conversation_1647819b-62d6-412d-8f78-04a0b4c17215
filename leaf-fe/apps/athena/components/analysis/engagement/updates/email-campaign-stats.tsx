import React, { useCallback } from 'react';
import {
  Tick,
  Typo<PERSON>,
  Tooltip as <PERSON><PERSON><PERSON>tip,
  Button,
  EmptyStateIcon,
} from '@ds';
import { QuestionMarkCircleIcon } from '@heroicons/react/outline';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import numeral from 'numeral';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  LegendProps,
  Line,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import { Payload } from 'recharts/types/component/DefaultLegendContent';
import { DataKey } from 'recharts/types/util/types';
import EngagementAnalyticsComponentWrapper from '@/components/analysis/engagement/engagement-analytics-component-wrapper';
import { useEngagementAnalyticsUpdatesContext } from '@/components/analysis/engagement/updates/context';
import PermissionWrapper from '@/components/layouts/permission-wrapper';
import { Permissions } from '@/hooks/use-permission';
import { INTERCOM_ARTICLES } from '@/utils/intercom-articles';
import routes from '@/utils/routes';
interface ModifiedPayload extends Payload {
  dataKey: DataKey<string>;
  payload: Payload['payload'];
}
interface ModifiedLegendProps extends LegendProps {
  payload?: Array<ModifiedPayload>;
}

const colors = {
  ctr: '#05603A',
  openRate: '#32D583',
  sends: '#B9E6FE',
};

const NoEmailSendsPlaceholder = () => {
  const {
    query: { marketListingKey },
  } = useRouter();

  const { dateRangeText } = useEngagementAnalyticsUpdatesContext();

  return (
    <div className="grid place-items-center gap-4 p-32">
      <EmptyStateIcon />
      <Typography
        className="text-center md:max-w-[40%]"
        variant="text-heading-md"
      >
        You haven’t distributed any updates via email in{' '}
        <span className="lowercase">{dateRangeText.chart}</span>
      </Typography>
      <Typography
        className="text-center text-gray-500 md:max-w-[40%]"
        variant="text-button-sm"
      >
        Learn more about{' '}
        <a
          className="text-green-800"
          href={INTERCOM_ARTICLES.engagementAnalytics.overview}
          rel="noopener noreferrer"
          target="_blank"
        >
          best practices
        </a>{' '}
        for setting up automated update emails to make your workflow more
        efficient
      </Typography>
      <Button
        href={routes.settings.automatedDistribution.updates.viewSettings.href(
          marketListingKey as string
        )}
      >
        Go to distribution settings
      </Button>
    </div>
  );
};

const renderTooltipText = (type: DataKey<string>) => {
  switch (type) {
    case 'ctr':
      return 'Average percentage of recipients who click in your email compared to number of emails sent';
    case 'openRate':
      return 'Average percentage of recipients who open your email compared to number of emails sent';
    default:
      return 'Total number of emails sent to recipients (includes automatically or manually distributed)';
  }
};

const CustomTooltip: React.ComponentType<TooltipProps<number, string>> = (
  all
) => {
  const { active, label, payload } = all;

  const updates = (payload ?? []).reduce((acc, cur) => {
    if (cur?.payload?.updates.length) {
      return Array.from(new Set([...acc, ...cur.payload.updates]));
    }
    return acc;
  }, [] as { header: string; id: string }[]);

  const renderUpdates = useCallback(() => {
    if (!updates.length) {
      return null;
    }
    if (updates.length === 1) {
      return (
        <Typography
          key={updates[0].id}
          className="text-white"
          variant="text-caption-bold"
        >
          {updates[0].header}
        </Typography>
      );
    }
    return (
      <Typography
        key={updates[0].id}
        className="text-white"
        variant="text-caption-bold"
      >
        {updates.length} campaigns
      </Typography>
    );
  }, [updates]);

  if (active && payload && payload.length) {
    return (
      <div className="w-[240px] space-y-1 rounded-lg bg-gray-900 p-3 text-white shadow-lg">
        <Typography className="text-white" variant="text-caption-bold">
          {`${dayjs(label).format('D MMM YY')}`}
        </Typography>
        {renderUpdates()}
        {payload.map((pl) => (
          <div key={pl.dataKey} className="flex justify-between">
            <div className="flex items-center gap-1">
              <div
                className={clsx(
                  'w-3',
                  ['sends'].includes(pl.dataKey as string) ? 'h-3' : 'h-0.5'
                )}
                style={{
                  background: pl.dataKey
                    ? colors[pl.dataKey as keyof typeof colors]
                    : undefined,
                }}
              />
              <Typography variant="text-caption">{pl.name}</Typography>
            </div>
            <Typography variant="text-caption">
              {pl.dataKey === 'ctr' || pl.dataKey === 'openRate'
                ? numeral(pl.value).format('0,0%')
                : pl.value}
            </Typography>
          </div>
        ))}
      </div>
    );
  }

  return null;
};

const renderLegendIcon = (entry: ModifiedPayload) => {
  const { color, type } = entry;

  switch (type) {
    case 'circle':
      return (
        <span
          className="inline-block h-[12px] w-[12px] rounded-full"
          style={{ backgroundColor: color }}
        />
      );
    case 'rect':
      return (
        <span
          className="inline-block h-[12px] w-[12px]"
          style={{ backgroundColor: color }}
        />
      );
    default:
      return (
        <span className="inline-flex h-[12px] w-[12px] items-center">
          <span
            className="h-[2px] w-[12px]"
            style={{ backgroundColor: color }}
          />
        </span>
      );
  }
};

const CustomLegend: React.ComponentType<ModifiedLegendProps> = ({
  payload,
}) => {
  if (!payload) return null;

  return (
    <ul className="flex w-full justify-center gap-4">
      {payload.map((entry, index) => {
        return (
          <li
            key={`item-${index}`}
            className="flex items-center justify-between"
          >
            <span className="flex items-center gap-1">
              {renderLegendIcon(entry)}
              <Typography
                className="font-semibold text-gray-700"
                variant="text-label-sm"
              >
                {entry.value}
              </Typography>

              <UITooltip
                className="w-[220px]"
                description={renderTooltipText(entry.dataKey)}
              >
                <div className="cursor-pointer">
                  <QuestionMarkCircleIcon className="h-4 w-4 text-gray-400" />
                </div>
              </UITooltip>
            </span>
          </li>
        );
      })}
    </ul>
  );
};

const EngagementAnalyticsUpdatesEmailCampaignStats = () => {
  const {
    query: { marketListingKey },
  } = useRouter();

  const { dateRangeText, engagementAnalyticsUpdatesData, error, loading } =
    useEngagementAnalyticsUpdatesContext();

  const chartData =
    engagementAnalyticsUpdatesData?.updatesEmailDistributionStatistics;

  const hasSends = chartData?.find((data) => data.sends > 0);

  const renderContent = useCallback(() => {
    if (!hasSends) {
      return <NoEmailSendsPlaceholder />;
    }
    return (
      <div className="w-full overflow-x-auto p-6">
        <ResponsiveContainer height={500} minWidth={580}>
          <ComposedChart
            className="border-none"
            data={chartData}
            margin={{
              bottom: 40,
              left: 20,
              right: 20,
              top: 30,
            }}
          >
            <CartesianGrid stroke="#F3F4F6" />
            <XAxis
              axisLine={false}
              dataKey="date"
              label={{
                offset: -20,
                position: 'insideBottom',
                style: {
                  fontSize: '16px',
                  fontWeight: '600',
                },
                value: 'Date',
              }}
              minTickGap={20}
              scale="point"
              tick={<Tick hideLast dy={'0px'} fontSize="14" fontWeight="400" />}
              tickCount={5}
              tickFormatter={(v) => dayjs(v).format('D MMM YY')}
              tickLine={false}
              tickMargin={15}
            />

            <YAxis
              axisLine={false}
              domain={[0, (max: number) => (max ? max * 1.1 : 500)]}
              label={{
                angle: -90,
                offset: -10,
                position: 'insideLeft',
                style: {
                  fontSize: '16px',
                  fontWeight: '600',
                  textAnchor: 'middle',
                },
                value: 'Number of emails sent',
              }}
              tick={<Tick fontSize="14" />}
              tickFormatter={(v) => numeral(v).format('0,0')}
              tickLine={false}
              tickMargin={10}
              yAxisId="emailsSent"
            />

            <YAxis
              axisLine={{
                stroke: 'white',
              }}
              color="#111927"
              domain={[0, (max: number) => (max ? Math.min(max * 2, 1) : 1)]}
              label={{
                angle: -90,
                offset: 70,
                position: 'insideLeft',
                style: {
                  fontSize: '16px',
                  fontWeight: '600',
                  textAnchor: 'middle',
                },
                value: 'Percentage (%)',
              }}
              orientation="right"
              stroke="#6C737F"
              tick={{ stroke: '#6C737F', strokeWidth: 0.1 }}
              tickFormatter={(v) => numeral(v).format('0,0[.]0%')}
              tickLine={false}
              type="number"
              yAxisId="clickThroughRate"
            />

            <Legend
              content={<CustomLegend />}
              formatter={(value: string) => {
                return (
                  <Typography
                    className="inline text-gray-700"
                    variant="text-label-sm"
                  >
                    {value}
                  </Typography>
                );
              }}
              iconSize={12}
              verticalAlign="bottom"
              wrapperStyle={{ bottom: 10 }}
            />

            <Bar
              barSize={20}
              dataKey="sends"
              fill={colors.sends}
              isAnimationActive={false}
              name="Total emails sent"
              stackId="stack"
              yAxisId="emailsSent"
            />

            <Line
              dataKey="ctr"
              dot={false}
              isAnimationActive={false}
              legendType="plainline"
              name="Click-through rate"
              stroke={colors.ctr}
              strokeWidth={2}
              type="monotone"
              yAxisId="clickThroughRate"
            />
            <Line
              dataKey="openRate"
              dot={false}
              isAnimationActive={false}
              legendType="plainline"
              name="Open rate"
              stroke={colors.openRate}
              strokeWidth={2}
              type="monotone"
              yAxisId="clickThroughRate"
            />
            <Tooltip content={<CustomTooltip />} />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    );
  }, [chartData, hasSends]);

  return (
    <PermissionWrapper name={Permissions.commsEmailsAdmin}>
      <EngagementAnalyticsComponentWrapper
        cta={
          <Button
            className="w-full md:w-auto"
            href={routes.settings.automatedDistribution.updates.viewSettings.href(
              marketListingKey as string
            )}
            variant="secondary-gray"
          >
            Distribution Settings
          </Button>
        }
        dateRangeText={dateRangeText.chart}
        error={!!error}
        loading={!!loading}
        subtitle="View the reach and engagement of your update email campaigns"
        title="Update email campaign statistics"
      >
        {renderContent()}
      </EngagementAnalyticsComponentWrapper>
    </PermissionWrapper>
  );
};

export default EngagementAnalyticsUpdatesEmailCampaignStats;
