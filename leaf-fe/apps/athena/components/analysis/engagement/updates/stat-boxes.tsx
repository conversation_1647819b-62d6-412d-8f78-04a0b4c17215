import React from 'react';
import { StatCard, Typography } from '@ds';
import { ChatIcon, EyeIcon } from '@heroicons/react/solid';
import { useEngagementAnalyticsUpdatesContext } from '@/components/analysis/engagement/updates/context';

const EngagementAnalyticsUpdatesStatBoxes: React.ComponentType = () => {
  const { dateRangeText, engagementAnalyticsUpdatesData, error, loading } =
    useEngagementAnalyticsUpdatesContext();

  const {
    updatesAnalyticsStats: {
      totalLikes,
      totalLikesDifference,
      totalQuestions,
      totalQuestionsDifference,
      totalSurveyResponses,
      totalSurveyResponsesDifference,
      totalViews,
      totalViewsDifference,
    },
    updatesReleasedCount,
  } = engagementAnalyticsUpdatesData ?? {
    updatesAnalyticsStats: {},
  };

  const dateRangeTextString = dateRangeText.statBox.toLowerCase();

  const updatesReleasedCountNonNullable = updatesReleasedCount || 0;

  return (
    <div>
      <div className="mb-3">
        <Typography variant="text-heading-md">{`You released ${updatesReleasedCountNonNullable} update${
          updatesReleasedCountNonNullable <= 1 ? '' : 's'
        } in ${
          dateRangeText.statBox == dateRangeText.chart
            ? 'the ' + dateRangeTextString
            : dateRangeText.chart
        }`}</Typography>
      </div>
      <div className="w-full grid-cols-4 gap-4 space-y-4 lg:grid lg:space-y-0">
        <div className="col-span-1 flex w-full">
          <StatCard
            badgeNum={totalViewsDifference}
            error={!!error}
            icon={
              <div className="h-12 w-12 rounded-full bg-violet-50 p-3.5">
                <EyeIcon className="h-5 w-5 text-violet-700" />
              </div>
            }
            loading={loading}
            statNum={totalViews || 0}
            timePeriodText={dateRangeText.statBox}
            title="Views"
            tooltipText={`Total number of page views, including repeat visitors on updates in the ${dateRangeTextString}`}
          />
        </div>
        <div className="col-span-1 flex w-full">
          <StatCard
            badgeNum={totalQuestionsDifference}
            error={!!error}
            icon={
              <div className="h-12 w-12 rounded-full bg-violet-50 p-3.5">
                <ChatIcon className="h-5 w-5 text-violet-700" />
              </div>
            }
            loading={loading}
            statNum={totalQuestions || 0}
            timePeriodText={dateRangeText.statBox}
            title="Questions"
            tooltipText={`Total number of questions asked on updates in the ${dateRangeTextString}`}
          />
        </div>
        <div className="col-span-1 flex w-full">
          <StatCard
            badgeNum={totalLikesDifference}
            error={!!error}
            icon={
              <div className="h-12 w-12 rounded-full bg-violet-50 p-3.5 text-violet-700">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 18 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0.999376 11.9805H4.99969V18.9801C4.99969 19.2453 5.10505 19.4996 5.2926 19.6871C5.48015 19.8746 5.73453 19.98 5.99977 19.98H12.0002C12.2655 19.98 12.5198 19.8746 12.7074 19.6871C12.8949 19.4996 13.0003 19.2453 13.0003 18.9801V11.9805H17.0006C17.1889 11.9802 17.3733 11.9268 17.5326 11.8264C17.6919 11.7261 17.8196 11.5828 17.9012 11.4132C17.9828 11.2435 18.0148 11.0542 17.9937 10.8672C17.9725 10.6801 17.8991 10.5028 17.7817 10.3556L9.78106 0.356228C9.40003 -0.118743 8.59997 -0.118743 8.21894 0.356228L0.218315 10.3556C0.100945 10.5028 0.0274711 10.6801 0.0063308 10.8672C-0.0148095 11.0542 0.0172405 11.2435 0.0988006 11.4132C0.180361 11.5828 0.308124 11.7261 0.467423 11.8264C0.626721 11.9268 0.811095 11.9802 0.999376 11.9805Z"
                    fill="#6927DA"
                  />
                </svg>
              </div>
            }
            loading={loading}
            statNum={totalLikes || 0}
            timePeriodText={dateRangeText.statBox}
            title="Likes"
            tooltipText={`Total number of likes received on updates in the ${dateRangeTextString}`}
          />
        </div>
        <div className="col-span-1 flex w-full">
          <StatCard
            badgeNum={totalSurveyResponsesDifference}
            error={!!error}
            icon={
              <div className="h-12 w-12 rounded-full bg-violet-50 p-3.5 text-violet-700">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 18 22"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clipRule="evenodd"
                    d="M13.6641 2.81846C14.1392 2.86315 14.6124 2.9146 15.0835 2.97272C16.4327 3.13913 17.3996 4.29665 17.3996 5.6201V19.1002C17.3996 20.5913 16.1908 21.8002 14.6996 21.8002H3.29961C1.80844 21.8002 0.599609 20.5913 0.599609 19.1002V5.6201C0.599609 4.29665 1.56655 3.13913 2.91567 2.97271C3.3868 2.9146 3.85998 2.86315 4.3351 2.81846C4.76244 1.3074 6.15173 0.200195 7.79961 0.200195H10.1996C11.8475 0.200195 13.2368 1.3074 13.6641 2.81846ZM5.99961 3.8002C5.99961 2.80608 6.8055 2.0002 7.79961 2.0002H10.1996C11.1937 2.0002 11.9996 2.80608 11.9996 3.8002V4.4002H5.99961V3.8002Z"
                    fill="#6927DA"
                    fillRule="evenodd"
                  />
                </svg>
              </div>
            }
            loading={loading}
            statNum={totalSurveyResponses || 0}
            timePeriodText={dateRangeText.statBox}
            title="Survey responses"
            tooltipText={`Total number of survey responses received on updates in the ${dateRangeTextString}`}
          />
        </div>
      </div>
    </div>
  );
};

export default EngagementAnalyticsUpdatesStatBoxes;
