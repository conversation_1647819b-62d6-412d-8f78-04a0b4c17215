import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  ArrowUpIcon,
  TwitterIcon,
  LinkedinIcon,
  Button,
  Tooltip,
} from '@ds';
import {
  ChatIcon,
  EyeIcon,
  ClipboardIcon,
  AtSymbolIcon,
} from '@heroicons/react/outline';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import { EngagementAnalyticsUpdatesQuery } from '@/apollo/generated';
import EngagementAnalyticsComponentWrapper from '@/components/analysis/engagement/engagement-analytics-component-wrapper';
import { useEngagementAnalyticsUpdatesContext } from '@/components/analysis/engagement/updates/context';
import TableCellRowLinkWrapper from '@/components/utils/tables/link-wrapper';
import TableHeader, { Header } from '@/components/utils/tables/table-header';
import routes from '@/utils/routes';

const EngagementAnalyticsUpdatesTop3: React.ComponentType = () => {
  const { dateRangeText, engagementAnalyticsUpdatesData, error, loading } =
    useEngagementAnalyticsUpdatesContext();

  const {
    query: { marketListingKey },
  } = useRouter();

  const { topThreeViewedUpdates } = engagementAnalyticsUpdatesData ?? {};

  const HEADERS: Header[] = [
    {
      className: 'w-[45%] md:min-w-[250px] ld:min-w-[360px] whitespace-nowrap',
      label: 'Update name',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Views',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Questions',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Likes',
    },
    {
      className: 'w-[11%] whitespace-nowrap pl-0',
      label: 'Survey',
    },
    {
      className: 'w-[11%] whitespace-nowrap',
      label: 'Distribution',
    },
  ];

  const renderUpdateRow = (
    update: EngagementAnalyticsUpdatesQuery['topThreeViewedUpdates'][0],
    index: number
  ) => {
    const href = routes.engagement.interactiveMedia.update.href(
      marketListingKey as string,
      update.id
    );
    return (
      <tr
        key={update.id}
        className="table-row border-b bg-white text-gray-500 first:border-t last:border-0 hover:cursor-pointer hover:bg-gray-50 md:first:border-t-0"
      >
        <td className="table-cell py-3 pl-3 text-gray-700 lg:pl-4">
          <TableCellRowLinkWrapper
            href={routes.engagement.interactiveMedia.update.href(
              marketListingKey as string,
              update.id
            )}
          >
            <div className="flex items-center gap-6">
              <Typography className="text-gray-900" variant="text-heading-md">
                {index + 1}
              </Typography>
              <div>
                <Typography
                  className="max-w-[400px] truncate whitespace-nowrap"
                  variant="text-button-sm"
                >
                  {update.title}
                </Typography>
              </div>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <EyeIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {update.totalViewCount.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <ChatIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {update.totalQuestionCount.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <ArrowUpIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {update.likes.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2">
              <ClipboardIcon className="h-4 w-4" />
              <Typography variant="text-body-sm">
                {update.totalSurveyResponses.toLocaleString()}
              </Typography>
            </div>
          </TableCellRowLinkWrapper>
        </td>
        <td className="table-cell py-4 pr-3">
          <TableCellRowLinkWrapper href={href}>
            <div className="flex items-center gap-2.5">
              {!update.distributedSocial?.twitterPostId &&
              !update.email &&
              !update.distributedSocial?.linkedinPostId
                ? '-'
                : null}
              {update.email ? (
                <Tooltip
                  className="min-w-[170px] max-w-[220px]"
                  content={
                    <>
                      <Typography className="text-white" variant="text-caption">
                        {`Email sent ${
                          update.email.sentAt
                            ? dayjs(update.email.sentAt).format('DD/MM/YYYY')
                            : null
                        }`}
                      </Typography>
                      <Typography
                        className="truncate text-white"
                        variant="text-caption"
                      >
                        {update.email.subject
                          ? update.email.subject.replace(
                              '{{ update_title }}',
                              update.title
                            )
                          : null}
                      </Typography>
                    </>
                  }
                >
                  <div className="cursor-pointer">
                    <AtSymbolIcon className="h-4 w-4 stroke-chart-orange-dark" />
                  </div>
                </Tooltip>
              ) : null}
              {update.distributedSocial?.twitterPostId &&
              update.distributedSocial?.twitterPostedAt ? (
                <Tooltip
                  className="min-w-[170px] max-w-[220px]"
                  description={`Posted to Twitter ${dayjs(
                    update.distributedSocial.twitterPostedAt
                  ).format('DD/MM/YYYY')}`}
                  place="top-start"
                >
                  <div className="cursor-pointer">
                    <TwitterIcon className="h-4 w-4 text-[#1DA1F2]" />
                  </div>
                </Tooltip>
              ) : null}
              {update.distributedSocial?.linkedinPostId &&
              update.distributedSocial?.linkedinPostedAt ? (
                <Tooltip
                  className="min-w-[170px] max-w-[220px]"
                  description={`Posted to LinkedIn ${dayjs(
                    update.distributedSocial.linkedinPostedAt
                  ).format('DD/MM/YYYY')}`}
                  place="top-start"
                >
                  <div className="cursor-pointer">
                    <LinkedinIcon className="h-4 w-4 text-blue-700" />
                  </div>
                </Tooltip>
              ) : null}
            </div>
          </TableCellRowLinkWrapper>
        </td>
      </tr>
    );
  };

  return (
    <EngagementAnalyticsComponentWrapper
      cta={
        <Button
          href={routes.engagement.interactiveMedia.updates.href(
            marketListingKey as string
          )}
          variant="secondary-gray"
        >
          View all updates
        </Button>
      }
      dateRangeText={dateRangeText.chart}
      error={!!error}
      loading={!!loading}
      title={`Your top ${topThreeViewedUpdates?.length ?? '3'} viewed updates`}
    >
      {/* Table */}
      <div
        data-private
        className="min-w-full overflow-hidden rounded-lg bg-white"
      >
        <div className="overflow-x-auto">
          <div className="sticky top-[55px] z-10 md:border-b"></div>
          <table className="table min-w-full">
            <TableHeader
              headers={HEADERS}
              textColor="text-gray-500"
              textVariant="text-button-sm"
            />
            <tbody className="table-row-group">
              {topThreeViewedUpdates && topThreeViewedUpdates.length
                ? topThreeViewedUpdates.map((update, index) =>
                    renderUpdateRow(update, index)
                  )
                : null}
            </tbody>
          </table>
        </div>
      </div>
    </EngagementAnalyticsComponentWrapper>
  );
};

export default EngagementAnalyticsUpdatesTop3;
