import React from 'react';
import { IncludedMediaUpdateIcon, Tooltip, Typography } from '@ds';
import { QuestionMarkCircleIcon } from '@heroicons/react/outline';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip as ChartTooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import { IncludedMediaUpdateType } from '@/apollo/generated';
import EngagementAnalyticsComponentWrapper from '@/components/analysis/engagement/engagement-analytics-component-wrapper';
import { useEngagementAnalyticsUpdatesContext } from '@/components/analysis/engagement/updates/context';
import { INTERCOM_ARTICLES } from '@/utils/intercom-articles';

function formatNumber(value: number) {
  return new Intl.NumberFormat('en-AU').format(value);
}

const renderTooltipTitle = (includedMediaType: IncludedMediaUpdateType) => {
  switch (includedMediaType) {
    case IncludedMediaUpdateType.Video:
      return 'video only';
    case IncludedMediaUpdateType.Url:
      return 'link only';
    case IncludedMediaUpdateType.Pdf:
      return 'pdf only';
    case IncludedMediaUpdateType.Image:
      return 'image only';
    case IncludedMediaUpdateType.Multi:
      return 'multi-media';
    default:
      return 'text only';
  }
};

const CustomTooltip: React.ComponentType<TooltipProps<string, number>> = (
  all
) => {
  const { active, payload } = all;

  if (active && payload && payload.length) {
    const currentData = payload[0].payload;

    return (
      <div className="w-[240px] rounded-lg bg-gray-900 p-3 text-white shadow-lg">
        <div>
          <Typography className="mb-1" variant="text-caption-bold">
            {`Updates with ${renderTooltipTitle(
              currentData.includedMediaType
            )}`}
          </Typography>
          <div className="flex justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-amber-500" />
              <Typography variant="text-caption">Views</Typography>
            </div>
            <Typography variant="text-caption">
              {formatNumber(currentData.totalViews)}
            </Typography>
          </div>
          <div className="flex justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-amber-200" />
              <Typography variant="text-caption">Engagement</Typography>
            </div>
            <Typography variant="text-caption">
              {formatNumber(currentData.totalEngagement)}
            </Typography>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

interface MediaTypeBarProps {
  data:
    | {
        includedMediaType: IncludedMediaUpdateType;
        totalEngagement: number;
        totalViews: number;
      }[]
    | undefined;
  max: number;
  title: string;
}

const MediaTypeBar: React.FC<MediaTypeBarProps> = ({ data, max, title }) => {
  if (!data) {
    return null;
  }

  return (
    <div className="h-[100px] p-6">
      <div className="flex items-center gap-1">
        <Typography variant="text-label-sm">{title}</Typography>
        <div>
          <IncludedMediaUpdateIcon
            includedMediaUpdateType={data[0].includedMediaType}
          />
        </div>
      </div>
      <div className="flex h-full w-full gap-1">
        <ResponsiveContainer width="100%">
          <BarChart
            data={data}
            layout="vertical"
            margin={{
              bottom: -10,
              right: 50,
              top: -10,
            }}
          >
            <ChartTooltip
              content={<CustomTooltip />}
              cursor={{ fill: 'transparent' }}
            />
            <YAxis hide dataKey="type" type="category" />
            <XAxis hide domain={[0, max]} type="number" />
            <Bar barSize={16} dataKey="totalViews" fill="#F79009" />
            <Bar barSize={16} dataKey="totalEngagement" fill="#FEDF89" />
          </BarChart>
        </ResponsiveContainer>
        <div className="flex h-full flex-col items-center gap-1 py-1">
          <Typography className="text-gray-700" variant="text-body-sm">
            {formatNumber(data[0].totalViews)}
          </Typography>
          <Typography className="text-gray-700" variant="text-body-sm">
            {formatNumber(data[0].totalEngagement)}
          </Typography>
        </div>
      </div>
    </div>
  );
};

const EngagementAnalyticsUpdatesMediaTypes: React.ComponentType = () => {
  const { dateRangeText, error, loading, updatesEngagementByContentTypeData } =
    useEngagementAnalyticsUpdatesContext();

  const chartData =
    updatesEngagementByContentTypeData?.updatesEngagementByContentType;

  const max = chartData
    ? Math.max(
        ...chartData.map((data) =>
          Math.max(data.totalViews, data.totalEngagement)
        )
      )
    : 0;

  const multiMediaData = chartData?.filter(
    (data) => data.includedMediaType === IncludedMediaUpdateType.Multi
  );

  const hasMultiMediaData = chartData?.some(
    (mmd) => mmd.totalViews > 0 || mmd.totalEngagement > 0
  );

  const videosData = chartData?.filter(
    (data) => data.includedMediaType === IncludedMediaUpdateType.Video
  );
  const hasVideosData = videosData?.some(
    (vd) => vd.totalViews > 0 || vd.totalEngagement > 0
  );

  const linksData = chartData?.filter(
    (data) => data.includedMediaType === IncludedMediaUpdateType.Url
  );
  const hasLinksData = linksData?.some(
    (ld) => ld.totalViews > 0 || ld.totalEngagement > 0
  );

  const pdfsData = chartData?.filter(
    (data) => data.includedMediaType === IncludedMediaUpdateType.Pdf
  );
  const hasPdfsData = pdfsData?.some(
    (pd) => pd.totalViews > 0 || pd.totalEngagement > 0
  );

  const imagesData = chartData?.filter(
    (data) => data.includedMediaType === IncludedMediaUpdateType.Image
  );

  const hasImagesData = imagesData?.some(
    (id) => id.totalViews > 0 || id.totalEngagement > 0
  );
  const textsData = chartData?.filter(
    (data) => data.includedMediaType === IncludedMediaUpdateType.None
  );
  const hasTextsData = textsData?.some(
    (td) => td.totalViews > 0 || td.totalEngagement > 0
  );

  // TODO: double check intercom article link here
  return (
    <EngagementAnalyticsComponentWrapper
      dateRangeText={dateRangeText.chart}
      error={!!error}
      loading={!!loading}
      subtitle="Compare how different types of media work to increase investor reach and engagement. Learn more about best practices "
      subtitleLearnMoreText="here"
      subtitleLearnMoreUrl={INTERCOM_ARTICLES.engagementAnalytics.updates}
      title="Engagement by media type"
    >
      {hasMultiMediaData && (
        <MediaTypeBar
          data={multiMediaData}
          max={max}
          title="Multiple media types"
        />
      )}
      {hasVideosData && (
        <MediaTypeBar data={videosData} max={max} title="Video only" />
      )}
      {hasLinksData && (
        <MediaTypeBar data={linksData} max={max} title="Link only" />
      )}
      {hasPdfsData && (
        <MediaTypeBar data={pdfsData} max={max} title="PDF only" />
      )}
      {hasImagesData && (
        <MediaTypeBar data={imagesData} max={max} title="Image only" />
      )}
      {hasTextsData && (
        <MediaTypeBar data={textsData} max={max} title="Text only" />
      )}
      <div className="flex items-center justify-center gap-3 p-4">
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 bg-amber-500" />
          <Typography className="text-gray-700" variant="text-label-sm">
            Views
          </Typography>
        </div>
        <div className="flex items-center gap-2">
          <div className="h-3 w-3 bg-amber-200" />
          <Typography className="text-gray-700" variant="text-label-sm">
            Engagement
          </Typography>
          <Tooltip
            className="w-[220px]"
            description="The combined questions, likes, and survey responses per media type"
          >
            <div className="cursor-pointer">
              <QuestionMarkCircleIcon className="h-4 w-4 text-gray-400" />
            </div>
          </Tooltip>
        </div>
      </div>
    </EngagementAnalyticsComponentWrapper>
  );
};

export default EngagementAnalyticsUpdatesMediaTypes;
