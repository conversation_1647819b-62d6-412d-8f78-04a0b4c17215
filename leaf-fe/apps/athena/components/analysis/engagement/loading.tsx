// DS V2
import React from 'react';

import { CircularProgress, Typography } from '@ds';

interface Props {
  customText?: string;
}

const EngagementAnalyticsLoading: React.ComponentType<Props> = ({
  customText,
}) => {
  return (
    <div className="flex h-[540px] flex-col items-center justify-center space-y-3">
      <CircularProgress color="amplify-green" size="lg" />
      <Typography className="text-gray-500" variant="text-body-md">
        {customText ? customText : 'Loading your data...'}
      </Typography>
    </div>
  );
};

export default EngagementAnalyticsLoading;
