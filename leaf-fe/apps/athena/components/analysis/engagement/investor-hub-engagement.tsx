import React, { useMemo, useState } from 'react';
import { Checkbox, Tick, Typography, Tooltip } from '@ds';
import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/router';
import numeral from 'numeral';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip as ChartTooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import { CategoricalChartFunc } from 'recharts/types/chart/generateCategoricalChart';
import EngagementAnalyticsEmpty from '@/components/analysis/engagement/empty';
import EngagementAnalyticsComponentWrapper from '@/components/analysis/engagement/engagement-analytics-component-wrapper';
import { useEngagementAnalyticsInvestorHubContext } from '@/components/analysis/engagement/investorhub-context';
import EngagementAnalyticsLoading from '@/components/analysis/engagement/loading';
import PermissionWrapper from '@/components/layouts/permission-wrapper';
import useBreakpoint from '@/components/pdf-viewer/use-breakpoint';
import { Permissions, usePermissions } from '@/hooks/use-permission';
import { INTERCOM_ARTICLES } from '@/utils/intercom-articles';
import routes from '@/utils/routes';

const viewsColor = '#A6F4C5';
const totalUniqueVisitorsColor = '#12B76A';
const signupsColor = '#05603A';
const sharepriceColor = '#384250';
const commsColor = '#384250';
const newTooltipThreshold = 150;

type Announcement = {
  announcementId: string;
  date: string;
  header: string;
};

type Update = {
  date: string;
  title: string;
  updateId: string;
};
type Campaign = {
  campaignId: string;
  date: string;
  name: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CommsDot = (props: any) => {
  const { cx, cy, payload } = props;
  if (payload?.comms) {
    // return white circle with block border
    return (
      <circle
        cx={cx}
        cy={cy}
        fill={commsColor}
        r={4}
        stroke="black"
        strokeWidth="1"
      />
    );
  }
  return null;
};

const CustomTooltip = ({
  active,
  label,
  payload,
  showComms,
  showSharePrice,
  showSignups,
  showUniqueVisitors,
  showViews,
  ticker,
}: // https://github.com/recharts/recharts/issues/2796
// eslint-disable-next-line @typescript-eslint/no-explicit-any
TooltipProps<any, any> & {
  showComms: boolean;
  showSharePrice: boolean;
  showSignups: boolean;
  showUniqueVisitors: boolean;
  showViews: boolean;
  ticker: string;
}) => {
  if (active && payload && payload.length) {
    const sharePrice = payload.find((p) => p.dataKey === 'close')?.value;

    const hasSharePrice = sharePrice !== null && sharePrice !== undefined;

    return (
      <div
        className="w-[240px] space-y-1 rounded-lg bg-gray-900 p-4"
        id="custom-tooltip"
      >
        <Typography className="text-white" variant="text-caption-bold">
          {`${dayjs(label).format('D MMM YY')}`}
        </Typography>
        {showViews && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-green-200" />
              <Typography className="text-white" variant="text-caption">
                Views
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalViews')?.value
              ).format('0,0[.][00]')}
            </Typography>
          </div>
        )}
        {showUniqueVisitors && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-green-400"></div>
              <Typography className="text-white" variant="text-caption">
                Unique visitors
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'totalUniqueVisitors')?.value
              ).format('0,0[.][00]')}
            </Typography>
          </div>
        )}
        {showSignups && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-3 w-3 bg-green-700" />
              <Typography className="text-white" variant="text-caption">
                Sign ups
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(
                payload.find((p) => p.dataKey === 'signups')?.value
              ).format('0,0[.][00]')}
            </Typography>
          </div>
        )}
        {showSharePrice && hasSharePrice && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <div className="h-2 w-3 bg-gray-700" />
              <Typography className="text-white" variant="text-caption">
                Share price
              </Typography>
            </div>
            <Typography className="text-white" variant="text-caption">
              {numeral(sharePrice).format('0,0[.]00[0]')}
            </Typography>
          </div>
        )}
        {showComms && (
          <>
            {payload
              .find((p) => p.dataKey === 'comms')
              ?.payload?.announcements.map(
                (announcement: Announcement, index: number) => (
                  <div key={announcement.announcementId}>
                    <Link
                      className="group inline"
                      href={routes.engagement.interactiveMedia.announcement.href(
                        ticker,
                        announcement.announcementId
                      )}
                      style={{ pointerEvents: 'all' }}
                    >
                      <span
                        className={`mr-1 inline-block h-2 w-2 rounded-full ${
                          index === 0 ? 'bg-gray-700' : 'bg-gray-900'
                        }`}
                      />
                      <Typography
                        className="inline text-white group-hover:cursor-pointer group-hover:underline"
                        variant="text-hyperlink-caption"
                      >
                        {announcement.header}
                      </Typography>
                    </Link>
                  </div>
                )
              )}
            {payload
              .find((p) => p.dataKey === 'comms')
              ?.payload?.updates.map((update: Update, index: number) => (
                <div key={update.updateId}>
                  <Link
                    className="group inline"
                    href={routes.engagement.interactiveMedia.update.href(
                      ticker,
                      update.updateId
                    )}
                    style={{ pointerEvents: 'all' }}
                  >
                    <Typography
                      className="inline text-white group-hover:cursor-pointer group-hover:underline"
                      variant="text-hyperlink-caption"
                    >
                      <span
                        className={`mr-1 inline-block h-2 w-2 rounded-full ${
                          index === 0 ? 'bg-gray-700' : 'bg-gray-900'
                        }`}
                      />
                      {update.title}
                    </Typography>
                  </Link>
                </div>
              ))}
            {payload
              .find((p) => p.dataKey === 'comms')
              ?.payload?.campaigns.map((campaign: Campaign, index: number) => (
                <div key={campaign.campaignId}>
                  <Link
                    className="group inline"
                    href={routes.engagement.campaigns.campaign.href(
                      ticker,
                      campaign.campaignId
                    )}
                    style={{ pointerEvents: 'all' }}
                  >
                    <Typography
                      className="inline text-white group-hover:cursor-pointer group-hover:underline"
                      variant="text-hyperlink-caption"
                    >
                      <span
                        className={`mr-1 inline-block h-2 w-2 rounded-full ${
                          index === 0 ? 'bg-gray-700' : 'bg-gray-900'
                        }`}
                      />
                      {campaign.name}
                    </Typography>
                  </Link>
                </div>
              ))}
          </>
        )}
      </div>
    );
  }

  return null;
};

// This function is a modified version of recharts' internal tooltip position calculation function
const getTranslate = ({
  coordinate,
  key,
  offset,
  tooltipDimension,
  viewBox,
  viewBoxDimension,
}: {
  coordinate: {
    x: number;
    y: number;
  };
  key: 'x' | 'y';
  offset: number;
  tooltipDimension: number;
  viewBox: {
    x: number;
    y: number;
  };
  viewBoxDimension: number;
}) => {
  const restricted = coordinate[key] - tooltipDimension - offset;
  const unrestricted = coordinate[key] + offset;
  // tooltipBoundary is the coordinate of the rightmost/bottommost edge of the tooltip
  // rightmost if key === 'x'
  // bottommost if key === 'y'
  const tooltipBoundary = coordinate[key] + tooltipDimension + offset;
  // viewBoxBoundary is the coordinate of the rightmost/bottommost edge of the viewbox
  // rightmost if key === 'x'
  // bottommost if key === 'y'
  const viewBoxBoundary = viewBox[key] + viewBoxDimension;

  // If we are past the boundary, then use the restricted coordinate
  if (tooltipBoundary > viewBoxBoundary) {
    return Math.max(restricted, viewBox[key]);
  }
  // Otherwise use the unrestricted coordinate
  return Math.max(unrestricted, viewBox[key]);
};

const InvestorHubEngagement: React.ComponentType = () => {
  const {
    query: { marketListingKey },
  } = useRouter();

  const containerRef = React.useRef<{ current: HTMLDivElement }>();

  const breakPoint = useBreakpoint();
  const isMobile = breakPoint ? ['sm', 'md'].includes(breakPoint) : false;

  const [tooltipMeasurements, setTooltipMeasurements] = useState({
    height: 0,
    width: 0,
  });

  const customTooltip = document.getElementById('custom-tooltip');

  React.useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      if (entries.length > 0) {
        if (
          entries[0].contentRect &&
          entries[0].contentRect.height &&
          entries[0].contentRect.width
        ) {
          if (entries[0].contentRect.height > tooltipMeasurements.height) {
            // In some cases our resize will occur AFTER the new position of the tooltip is calculated
            // If this happens, we can potentially be using the old height to calculate position, leading to an overflow
            // if the new height is larger
            // So we do a manual update if the new height is greater
            setTooltipPosition(({ x, y }) => ({
              x: x,
              y: Math.max(
                y -
                  (entries[0].contentRect.height - tooltipMeasurements.height),
                0
              ),
            }));
          }
          setTooltipMeasurements({
            height: entries[0].contentRect.height,
            width: entries[0].contentRect.width,
          });
        }
      }
    });

    if (customTooltip) {
      resizeObserver.observe(customTooltip);
    }

    const unobserve = () => {
      if (customTooltip) {
        resizeObserver.unobserve(customTooltip);
      }
    };
    return unobserve;
  }, [customTooltip, tooltipMeasurements]);

  // tooltipPosition is a set of coordinates relative to the viewbox
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  const maybeUpdateTooltipPosition: CategoricalChartFunc = (props) => {
    // This provides the rectangle dimensions of the viewbox of the chart
    // We need this because recharts doesn't provide the width/height of the viewbox etc
    // We call this to get the height and width of the viewbox
    const viewBoxBoundingClientRect =
      containerRef?.current?.current.getBoundingClientRect();

    // chartX and chartY are coordinates relative to the viewbox
    const { activePayload, chartX, chartY } = props;

    const xCoordinate = getTranslate({
      coordinate: {
        x: chartX as number,
        y: chartY as number,
      },
      key: 'x',
      offset: -tooltipMeasurements.width / 4,
      tooltipDimension: tooltipMeasurements.width,
      viewBox: {
        x: 0,
        y: 0,
      },
      viewBoxDimension: viewBoxBoundingClientRect?.width as number,
    });

    const yCoordinate = getTranslate({
      coordinate: {
        x: chartX as number,
        y: chartY as number,
      },
      key: 'y',
      offset: 0,
      tooltipDimension: tooltipMeasurements.height,
      viewBox: {
        x: 0,
        y: 0,
      },
      viewBoxDimension: viewBoxBoundingClientRect?.height as number,
    });

    // If the current payload has an announcement or update, don't change y position as we want to be able to click the announcement/update
    if (
      activePayload?.[0]?.payload?.announcements?.[0] ||
      activePayload?.[0]?.payload?.updates?.[0]
    ) {
      setTooltipPosition(({ y }) => ({ x: xCoordinate as number, y: y }));
    } else if (
      Math.abs((xCoordinate as number) - tooltipPosition.x) >
      newTooltipThreshold
    ) {
      // Otherwise, if the distance between new xCoordinate and the old xCoordinate position (tooltipPosition.x) is larger thann
      // the threshold, we want to update x and y
      if (yCoordinate) {
        setTooltipPosition({
          x: xCoordinate as number,
          y: yCoordinate as number,
        });
      }
    }
  };

  const { dateRangeText, engagementAnalyticsInvestorHubData, error, loading } =
    useEngagementAnalyticsInvestorHubContext();

  const chartData = useMemo(() => {
    if (engagementAnalyticsInvestorHubData?.investorHubEngagement) {
      // Make comms dot float on the top of chart
      const max =
        engagementAnalyticsInvestorHubData.investorHubEngagement.reduce(
          (prev, curr) => {
            const total =
              curr.signups + curr.totalViews + curr.totalUniqueVisitors;
            return Math.max(prev, total);
          },
          0
        );

      return engagementAnalyticsInvestorHubData.investorHubEngagement.map(
        (item) => {
          return {
            ...item,
            comms:
              item.announcements.length ||
              item.updates.length ||
              item.campaigns.length
                ? max + max * 0.1
                : null,
          };
        }
      );
    }
    return [];
  }, [engagementAnalyticsInvestorHubData?.investorHubEngagement]);

  const hasSomeCommsPermissions = usePermissions([
    Permissions.commsEmailsAdmin,
    Permissions.interactionsMediaAnnouncementsAdmin,
    Permissions.interactionsMediaAnnouncementsEditor,
    Permissions.interactionsMediaUpdatesAdmin,
  ]);

  const [showViews, toggleShowViews] = useState(true);
  const [showUniqueVisitors, toggleShowTotalUniqueVisitors] = useState(true);
  const [showSignUps, toggleShowSignups] = useState(true);
  const [showComms, toggleComms] = useState(
    hasSomeCommsPermissions ? true : false
  );
  const [showSharePrice, toggleShowSharePrice] = useState(true);

  const renderChart = () => {
    if (loading) {
      return <EngagementAnalyticsLoading />;
    }

    if (error || !chartData.length) {
      return <EngagementAnalyticsEmpty />;
    }

    return (
      <div className="h-[575px] w-full p-6">
        {chartData.length ? (
          <div className="flex flex-col flex-wrap gap-4 font-medium md:flex-row">
            <div className="flex items-center gap-1.5">
              <Checkbox
                checked={showViews ? 'yes' : 'no'}
                label={{ description: '', title: 'Views' }}
                size="sm"
                onClick={() => toggleShowViews(!showViews)}
              />

              <Tooltip
                content="Total page views, including multiple views from the same user"
                iconClassName="text-gray-400"
                id="tooltip-page-views"
              />
            </div>
            <div className="flex items-center gap-1.5">
              <Checkbox
                checked={showUniqueVisitors ? 'yes' : 'no'}
                label={{ description: '', title: 'Unique visitors' }}
                size="sm"
                onClick={() =>
                  toggleShowTotalUniqueVisitors(!showUniqueVisitors)
                }
              />

              <Tooltip
                content="Unique visits to your investor hub"
                iconClassName="text-gray-400"
                id="tooltip-unique-visits"
              />
            </div>
            <Checkbox
              checked={showSignUps ? 'yes' : 'no'}
              label={{ description: '', title: 'Sign ups' }}
              size="sm"
              onClick={() => toggleShowSignups(!showSignUps)}
            />

            <div className="flex items-center gap-1.5">
              <Checkbox
                checked={showSharePrice ? 'yes' : 'no'}
                label={{
                  description: '',
                  title: `Share price`,
                }}
                size="sm"
                onClick={() => toggleShowSharePrice(!showSharePrice)}
              />
            </div>
            <PermissionWrapper
              name={[
                Permissions.commsEmailsAdmin,
                Permissions.interactionsMediaAnnouncementsAdmin,
                Permissions.interactionsMediaAnnouncementsEditor,
                Permissions.interactionsMediaUpdatesAdmin,
              ]}
            >
              <div className="flex items-center gap-1.5">
                <Checkbox
                  checked={showComms ? 'yes' : 'no'}
                  label={{ description: '', title: 'Investor comms' }}
                  size="sm"
                  onClick={() => toggleComms(!showComms)}
                />

                <Tooltip
                  content="Release of announcements, updates, and any type of email, mail or distribution campaign"
                  iconClassName="text-gray-400"
                  id="tooltip-release-announcements"
                />
              </div>
            </PermissionWrapper>
          </div>
        ) : null}
        <div className="w-full overflow-auto lg:overflow-visible">
          <ResponsiveContainer
            ref={containerRef}
            height={isMobile ? 400 : 500}
            minWidth={580}
          >
            <ComposedChart
              className="border-none"
              data={chartData}
              margin={{
                bottom: 40,
                left: 20,
                right: 20,
                top: 30,
              }}
              onMouseMove={maybeUpdateTooltipPosition}
            >
              <CartesianGrid stroke="#F3F4F6" />
              <XAxis
                axisLine={false}
                dataKey="date"
                label={{
                  offset: -20,
                  position: 'insideBottom',
                  style: {
                    fontSize: '16px',
                    fontWeight: '600',
                  },
                  value: 'Date',
                }}
                minTickGap={20}
                scale={showSharePrice ? 'point' : 'band'}
                tick={
                  <Tick hideLast dy={'0px'} fontSize="14" fontWeight="400" />
                }
                tickCount={5}
                tickFormatter={(v) => dayjs(v).format('D MMM YY')}
                tickLine={false}
                tickMargin={15}
              />

              <YAxis
                axisLine={false}
                domain={[0, (max: number) => (max ? max * 1.1 : 500)]}
                label={{
                  angle: -90,
                  offset: -10,
                  position: 'insideLeft',
                  style: {
                    fontSize: '16px',
                    fontWeight: '600',
                    textAnchor: 'middle',
                  },
                  value: 'Audience (No.)',
                }}
                tick={<Tick fontSize="14" />}
                tickFormatter={(v) => numeral(v).format('0,0')}
                tickLine={false}
                tickMargin={10}
                yAxisId="totalAudiences"
              />

              {showSharePrice ? (
                <YAxis
                  axisLine={{
                    stroke: 'white',
                  }}
                  color="#6C737F"
                  domain={[0, (max: number) => max * 1.5]}
                  label={{
                    angle: -90,
                    offset: 70,
                    position: 'insideLeft',
                    style: {
                      fontSize: '16px',
                      fontWeight: '600',
                      textAnchor: 'middle',
                    },
                    value: `Share price ${
                      engagementAnalyticsInvestorHubData?.investorHubEngagement
                        ?.find((ele) => ele?.currency !== null)
                        ?.currency?.toString()
                        .replace(/(.+)/, '($1)') ?? ''
                    }`,
                  }}
                  orientation="right"
                  stroke="#6C737F"
                  tick={{ stroke: '#6C737F', strokeWidth: 0.1 }}
                  tickFormatter={(v) => numeral(v).format('0,0[.]00[0]')}
                  tickLine={false}
                  type="number"
                  yAxisId="sharePrice"
                />
              ) : null}

              <Legend
                formatter={(value: string) => {
                  return (
                    <Typography
                      className="inline text-gray-700"
                      variant="text-label-sm"
                    >
                      {value}
                    </Typography>
                  );
                }}
                iconSize={12}
                verticalAlign="bottom"
                wrapperStyle={{ bottom: 10 }}
              />

              {showSignUps ? (
                <Bar
                  barSize={20}
                  dataKey="signups"
                  fill={signupsColor}
                  isAnimationActive={false}
                  name="Sign ups"
                  stackId="stack"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {showUniqueVisitors ? (
                <Bar
                  barSize={20}
                  dataKey="totalUniqueVisitors"
                  fill={totalUniqueVisitorsColor}
                  isAnimationActive={false}
                  name="Unique visitors"
                  stackId="stack"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {showViews ? (
                <Bar
                  barSize={20}
                  dataKey="totalViews"
                  fill={viewsColor}
                  isAnimationActive={false}
                  name="Views"
                  stackId="stack"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {/* Line is after the bar so it rendered on top of the bar chart */}
              {showComms ? (
                <Line
                  activeDot={{
                    fill: 'white',
                    r: 4,
                    stroke: commsColor,
                    strokeWidth: 2,
                  }}
                  dataKey="comms"
                  dot={showComms ? <CommsDot /> : false}
                  isAnimationActive={false}
                  legendType="circle"
                  name="Investor comms"
                  stroke={commsColor}
                  strokeWidth={0}
                  type="monotone"
                  yAxisId="totalAudiences"
                />
              ) : null}

              {showSharePrice ? (
                <Line
                  connectNulls
                  dataKey="close"
                  dot={false}
                  isAnimationActive={false}
                  legendType="plainline"
                  name="Share price"
                  stroke={sharepriceColor}
                  strokeWidth={2}
                  type="monotone"
                  yAxisId="sharePrice"
                />
              ) : null}

              <ChartTooltip
                content={
                  <CustomTooltip
                    showComms={showComms}
                    showSharePrice={showSharePrice}
                    showSignups={showSignUps}
                    showUniqueVisitors={showUniqueVisitors}
                    showViews={showViews}
                    ticker={marketListingKey as string}
                  />
                }
                position={{
                  x: tooltipPosition.x,
                  y: tooltipPosition.y,
                }}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  };

  return (
    <EngagementAnalyticsComponentWrapper
      dateRangeText={dateRangeText.chart}
      error={!!error}
      loading={!!loading}
      subtitle="Compare your engagement analytics with your share price and investor
    communications"
      subtitleLearnMoreUrl={INTERCOM_ARTICLES.engagementAnalytics.overview}
      title="Investor hub engagement"
    >
      {renderChart()}
    </EngagementAnalyticsComponentWrapper>
  );
};

export default InvestorHubEngagement;
