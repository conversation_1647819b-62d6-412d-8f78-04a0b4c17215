import React, { createContext, useContext, useState } from 'react';
import { ApolloError } from '@apollo/client';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import {
  EngagementAnalyticsOverviewQuery,
  useEngagementAnalyticsOverviewQuery,
} from '@/apollo/generated';
import { DateRange } from '@/components/analysis/engagement/date-picker';

interface Props {
  children?: React.ReactNode;
}

interface OverviewContextType {
  dateRangeText: { chart: string; statBox: string };
  endDate: Maybe<Date>;
  engagementAnalyticsOverviewData?: EngagementAnalyticsOverviewQuery;
  error?: ApolloError;
  loading?: boolean;
  maxDate?: Date;
  minDate?: Date;
  queryVariables: {
    dateRange: DateRange;
    endDate: Maybe<Date>;
    startDate: Maybe<Date>;
  };
  setEndDate: (endDate: Maybe<Date>) => void;
  setQueryVariables: (
    dateRange: DateRange,
    startDate: Maybe<Date>,
    endDate: Maybe<Date>
  ) => void;
  setStartDate: (startDate: Maybe<Date>) => void;
  startDate: Maybe<Date>;
}

const defaultContext: OverviewContextType = {
  dateRangeText: { chart: 'Last 30 days', statBox: 'Last 30 days' },
  endDate: null,
  queryVariables: {
    dateRange: 'Last 30 days',
    endDate: null,
    startDate: null,
  },
  setEndDate: () => null,
  setQueryVariables: () => null,
  setStartDate: () => null,
  startDate: null,
};

const OverviewContext = createContext<OverviewContextType>(defaultContext);

export const useEngagementAnalyticsOverviewContext = () =>
  useContext(OverviewContext);

export const EngagementAnalyticsOverviewProvider: React.FC<Props> = ({
  children,
}) => {
  const router = useRouter();

  const [endDate, setEndDate] = useState<Maybe<Date>>(
    router.query.endDate
      ? dayjs(router.query.endDate as string).toDate()
      : dayjs().toDate()
  );

  const [startDate, setStartDate] = useState<Maybe<Date>>(
    router.query.startDate
      ? dayjs(router.query.startDate as string).toDate()
      : dayjs().subtract(30, 'days').toDate()
  );

  const [queryVariables, setQueryVariables] = useState({
    dateRange: (router.query.dateRange as DateRange) || 'Last 30 days',
    endDate:
      router.query.dateRange === 'Choose a date range'
        ? dayjs(router.query.endDate as string).toDate()
        : null,
    startDate:
      router.query.dateRange === 'Choose a date range'
        ? dayjs(router.query.startDate as string).toDate()
        : null,
  });

  const customChartText = () =>
    `${startDate ? dayjs(startDate).format('D/M/YYYY') : ''} - ${
      endDate ? dayjs(endDate).format('D/M/YYYY') : ''
    }`;

  const customStatBoxText = () =>
    'Last ' + dayjs(endDate).diff(dayjs(startDate), 'days') + ' days period';

  const [dateRangeText, setDateRangeText] = useState({
    chart: router.query.dateRange
      ? router.query.dateRange === 'Choose a date range'
        ? customChartText()
        : (router.query.dateRange as string)
      : 'Last 30 Days',
    statBox: router.query.dateRange
      ? router.query.dateRange === 'Choose a date range'
        ? customStatBoxText()
        : (router.query.dateRange as string)
      : 'Last 30 Days',
  });

  const onSetQueryVariables = async (
    newDateRange: DateRange,
    newEndDate: Maybe<Date>,
    newStartDate: Maybe<Date>
  ) => {
    const { dateRange, endDate, startDate } = router.query;
    if (
      dateRange === newDateRange &&
      endDate === dayjs(newEndDate).format('YYYYMMDD') &&
      startDate === dayjs(newStartDate).format('YYYYMMDD')
    ) {
      return;
    } else {
      if (newDateRange === 'Choose a date range') {
        setQueryVariables({
          dateRange: newDateRange,
          endDate: newEndDate,
          startDate: newStartDate,
        });

        setDateRangeText({
          chart: customChartText(),
          statBox: customStatBoxText(),
        });

        await router.replace(
          {
            pathname: router.pathname,
            query: {
              ...router.query,
              dateRange: newDateRange,
              endDate: dayjs(newEndDate).format('YYYYMMDD'),
              startDate: dayjs(newStartDate).format('YYYYMMDD'),
            },
          },
          undefined,
          { shallow: true }
        );
      } else {
        setQueryVariables({
          dateRange: newDateRange,
          endDate: newEndDate,
          startDate: newStartDate,
        });

        setDateRangeText({
          chart: newDateRange,
          statBox: newDateRange,
        });

        const updatedQuery = { ...router.query };
        updatedQuery.dateRange = newDateRange;
        delete updatedQuery.endDate;
        delete updatedQuery.startDate;
        await router.replace(
          {
            pathname: router.pathname,
            query: updatedQuery,
          },
          undefined,
          { shallow: true }
        );
      }
    }
  };
  const { data, error, loading, networkStatus } =
    useEngagementAnalyticsOverviewQuery({
      skip: !startDate || !endDate,
      variables: {
        endDate: dayjs(endDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        startDate: dayjs(startDate)
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      },
    });

  return (
    <OverviewContext.Provider
      value={{
        dateRangeText,
        endDate,
        engagementAnalyticsOverviewData: data,
        error,
        loading: loading || [1, 2, 3, 4].includes(networkStatus),
        queryVariables,
        setEndDate: setEndDate,
        setQueryVariables: onSetQueryVariables,
        setStartDate: setStartDate,
        startDate,
      }}
    >
      {children}
    </OverviewContext.Provider>
  );
};
