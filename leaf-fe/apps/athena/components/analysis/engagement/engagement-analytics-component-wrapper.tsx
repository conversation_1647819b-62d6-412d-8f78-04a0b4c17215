import React, { useCallback } from 'react';
import { Tooltip, Typography } from '@ds';
import { QuestionMarkCircleIcon } from '@heroicons/react/outline';
import EngagementAnalyticsEmpty from '@/components/analysis/engagement/empty';
import EngagementAnalyticsLoading from '@/components/analysis/engagement/loading';

interface Props {
  children?: React.ReactNode;
  cta?: React.ReactNode;
  dateRangeText: string;
  error: boolean;
  loading: boolean;
  subtitle?: string;
  subtitleLearnMoreText?: string;
  subtitleLearnMoreUrl?: string;
  title: string;
  tooltipDescription?: string;
}

const EngagementAnalyticsComponentWrapper: React.FC<Props> = ({
  children,
  cta,
  dateRangeText,
  error,
  loading,
  subtitle,
  subtitleLearnMoreText = 'Learn more',
  subtitleLearnMoreUrl,
  title,
  tooltipDescription,
}) => {
  const renderContent = useCallback(() => {
    if (loading) return <EngagementAnalyticsLoading />;

    if (error) return <EngagementAnalyticsEmpty />;

    return children;
  }, [children, error, loading]);

  return (
    <div className="flex-1 rounded-lg border border-gray-200 bg-white">
      <div className="flex flex-col items-center justify-between gap-4 border-b border-b-gray-200 px-6 py-5 md:flex-row">
        <div>
          <div className="flex items-center gap-1">
            <Typography className="text-gray-900" variant="text-heading-sm">
              {title} <span className="lowercase">{dateRangeText}</span>
            </Typography>
            {tooltipDescription ? (
              <Tooltip
                className="w-[220px]"
                description={tooltipDescription}
                place="top-start"
              >
                <div className="cursor-pointer">
                  <QuestionMarkCircleIcon className="h-4 w-4 text-gray-900" />
                </div>
              </Tooltip>
            ) : null}
          </div>
          {subtitle ? (
            <Typography className="text-gray-500" variant="text-button-sm">
              {subtitle}
              {subtitleLearnMoreUrl ? (
                <>
                  {' '}
                  <a
                    className="text-amplify-green-900 "
                    href={subtitleLearnMoreUrl}
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    {subtitleLearnMoreText}
                  </a>
                </>
              ) : null}
            </Typography>
          ) : null}
        </div>
        {cta ? cta : null}
      </div>
      {renderContent()}
    </div>
  );
};

export default EngagementAnalyticsComponentWrapper;
