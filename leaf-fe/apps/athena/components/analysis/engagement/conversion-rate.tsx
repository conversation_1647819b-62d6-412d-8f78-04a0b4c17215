import React, { useC<PERSON>back, useMemo, useState } from 'react';
import { Typography } from '@ds';
import numeral from 'numeral';
import {
  Cell,
  Funnel,
  FunnelChart,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
} from 'recharts';

import EngagementAnalyticsEmpty from '@/components/analysis/engagement/empty';
import EngagementAnalyticsLoading from '@/components/analysis/engagement/loading';
import { useEngagementAnalyticsOverviewContext } from '@/components/analysis/engagement/overview-context';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import { INTERCOM_ARTICLES } from '@/utils/intercom-articles';

const EngagementAnalyticsConversionRate: React.ComponentType = () => {
  const [focusBar, setFocusBar] = useState<number | null | undefined>(null);
  const { dateRangeText, engagementAnalyticsOverviewData, error, loading } =
    useEngagementAnalyticsOverviewContext();
  const { isPremium } = useCurrentCompanyProfileUser();

  const getConversionRate = (
    toCategoryValue: number,
    fromCategoryValue?: number
  ) => {
    if (fromCategoryValue && toCategoryValue !== 0) {
      return (
        Math.round(((fromCategoryValue * 100) / toCategoryValue) * 100) / 100
      );
    }

    return 0;
  };

  // Note: "shareholders" name contains a hidden character for the hyphen
  // See: https://developer.mozilla.org/en-US/docs/Web/CSS/hyphens
  const data = useMemo(() => {
    return isPremium
      ? [
          {
            name: 'Unique Visitors',
            value:
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .totalUniqueVisitors ?? 0,
          },
          {
            name: 'Leads',
            value:
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .totalLeads ?? 0,
          },
          {
            name: 'Shareholders',
            shortName: 'Share- holders',
            value:
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .totalConvertedShareholders ?? 0,
          },
        ]
      : [
          {
            name: 'Unique Visitors',
            value:
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .totalUniqueVisitors ?? 0,
          },
          {
            name: 'Leads',
            value:
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .totalLeads ?? 0,
          },
          {
            name: 'Nominated shareholders',
            shortName: 'Nom. share- holders',
            value:
              engagementAnalyticsOverviewData?.engagementAnalyticsOverview
                .totalNominatedShareholders ?? 0,
          },
        ];
  }, [engagementAnalyticsOverviewData?.engagementAnalyticsOverview, isPremium]);

  const renderContent = useCallback(() => {
    if (loading) {
      return <EngagementAnalyticsLoading />;
    }

    if (error) {
      return <EngagementAnalyticsEmpty />;
    }

    const chartColors = ['#DDD6FE', '#A48AFB', '#6927DA'];

    const CustomTooltip: React.ComponentType<
      TooltipProps<number, string> & { total: number }
    > = (all) => {
      const { active, payload, total } = all;

      if (active && payload && payload.length) {
        const currentData = payload[0];
        const text = () => {
          if (currentData.name === 'Leads') return 'Unique visitors to leads:';
          if (currentData.name === 'Shareholders')
            return 'Unique visitors to converted shareholders:';
          if (currentData.name === 'Nominated shareholders')
            return 'Unique visitors to Nominated shareholders:';
          return `${currentData.name}:`;
        };
        return (
          <div className="w-[240px] rounded-lg bg-gray-900 p-3 text-white shadow-lg">
            <Typography className="mb-1" variant="text-caption-bold">
              <span>{dateRangeText.chart}</span>
            </Typography>

            <div className="flex justify-between">
              <Typography variant="text-caption">{text()}</Typography>
              <Typography variant="text-caption">
                <span>{`${numeral(currentData.value).format('0,0')} / ${numeral(
                  total
                ).format('0,0')} (${getConversionRate(
                  data[currentData.name === 'Shareholder' ? 1 : 0].value,
                  currentData.value
                )}%)`}</span>
              </Typography>
            </div>
          </div>
        );
      }

      return null;
    };

    return (
      <div className="flex min-h-[460px] flex-col justify-center p-6 2xl:min-h-[540px]">
        <div className="flex flex-1 flex-col justify-center">
          <div className="relative flex flex-1 flex-col justify-center">
            <div className="absolute left-0 h-[320px] w-[50px]">
              <div className="grid h-full grid-flow-row auto-rows-fr">
                {data.map((d) => (
                  <div key={d.name} className="flex items-center justify-end">
                    <div className="max-w-[50px] text-right text-gray-700">
                      <Typography variant="text-body-sm">
                        <span lang="en" style={{ hyphens: 'auto' }}>
                          {d.shortName || d.name}
                        </span>
                      </Typography>
                      <Typography variant="text-body-sm">
                        {numeral(d.value / data[0].value).format('0%')}
                      </Typography>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="ml-[60px] flex h-full w-[calc(100%-60px)] flex-1 flex-col justify-center border border-gray-100">
              <div className="h-[320px] transition-colors">
                <ResponsiveContainer width="100%">
                  <FunnelChart
                    margin={{ bottom: 0, left: 0, right: -25, top: -25 }}
                    onMouseLeave={() => setFocusBar(null)}
                  >
                    <Tooltip
                      content={
                        <CustomTooltip
                          total={
                            data.find((d) => d.name === 'Unique Visitors')
                              ?.value ?? 0
                          }
                        />
                      }
                      cursor={{ stroke: 'red', strokeWidth: 2 }}
                    />
                    <Funnel
                      isAnimationActive
                      data={data}
                      dataKey="value"
                      lastShapeType="rectangle"
                      orientation="vertical"
                      strokeWidth={2}
                      width={'100%'}
                      onMouseLeave={() => setFocusBar(null)}
                      onMouseMove={(state) => {
                        const active = data.find(
                          (points) => points.name === state.name
                        );
                        if (active) {
                          setFocusBar(data.indexOf(active) + 1);
                        } else {
                          setFocusBar(null);
                        }
                      }}
                    >
                      {data.map((_entry, index) => {
                        return (
                          <Cell
                            key={`cell-${index}`}
                            fill={
                              !focusBar
                                ? chartColors[index]
                                : focusBar - 1 === index
                                ? '#7839EE'
                                : '#ECE9FE'
                            }
                          />
                        );
                      })}
                    </Funnel>
                  </FunnelChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }, [loading, error, data, dateRangeText, focusBar]);

  return (
    <div className="flex-1 rounded-lg border border-gray-200 bg-white">
      <div className="border-b border-b-gray-200 px-6 py-5">
        <Typography className="text-gray-900" variant="text-heading-sm">
          Conversion rate{' '}
          <span className="lowercase">{dateRangeText.chart}</span>
        </Typography>
        <Typography className="text-gray-500" variant="text-button-sm">
          View your shareholder conversion funnel.{' '}
          <a
            className="text-amplify-green-900 "
            href={INTERCOM_ARTICLES.engagementAnalytics.leadConversion}
            rel="noopener noreferrer"
            target="_blank"
          >
            Learn more
          </a>
        </Typography>
      </div>
      {renderContent()}
    </div>
  );
};

export default EngagementAnalyticsConversionRate;
