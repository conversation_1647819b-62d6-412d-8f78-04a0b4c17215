import React, { useCallback, useMemo, useState } from 'react';
import { ButtonGroup, Typography, Badge } from '@ds';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CurrencyDollarIcon,
  EnvelopeIcon,
  FaceSmileIcon,
  ScaleIcon,
  UsersIcon,
  PresentationChartLineIcon,
  UserGroupIcon,
} from '@heroicons/react-v2/24/outline';
import clsx from 'clsx';
import numeral from 'numeral';
import { useCurrentCompanyStatsQuery } from '@/apollo/generated';
import { formatAverageHoldingTime } from '@/utils/common-helpers';

type Scenario = 'allGood' | 'rockyFinish' | 'allBad';

const ShareholderInsight: React.FC<{
  comparison?: number;
  comparisonFormat?: string;
  highlighted?: boolean;
  icon: React.ReactNode;
  larger?: boolean;
  loading: boolean;
  title: string;
  value: string;
}> = ({
  comparison,
  comparisonFormat,
  highlighted,
  icon,
  larger = false,
  loading,
  title,
  value,
}) => {
  return (
    <div className="flex h-full gap-4">
      <div
        className={clsx(
          'text-amplify-green-700 bg-amplify-green-50 flex shrink-0 items-center justify-center rounded-full',
          larger ? 'h-[100px] w-[100px]' : 'h-10 w-10'
        )}
      >
        {icon}
      </div>
      <div>
        {loading ? (
          <div className="h-4 w-10 animate-pulse bg-secondary-grey-light" />
        ) : (
          <Typography
            className={clsx(larger ? '!text-3xl font-semibold' : '')}
            variant="text-heading-md"
          >
            {value}
            {comparison ? (
              <span
                className={clsx(
                  'ml-2',
                  comparison === 0 && 'text-text-grey',
                  comparison > 0 && 'text-green-500',
                  comparison < 0 && 'text-red-500'
                )}
              >
                ({comparison > 0 ? '+' : ''}
                {comparisonFormat
                  ? numeral(comparison).format(comparisonFormat)
                  : comparison}
                )
              </span>
            ) : null}
          </Typography>
        )}
        <Typography
          className={clsx(
            highlighted ? 'text-blue-700' : 'text-gray-700',
            larger ? 'whitespace-nowrap' : ''
          )}
          variant="text-body-sm"
        >
          {title}
        </Typography>
      </div>
    </div>
  );
};

const ScenarioInsights: React.FC<{ scenario: Scenario }> = ({ scenario }) => {
  const { data, loading } = useCurrentCompanyStatsQuery();

  return (
    <div>
      {/* Main section */}
      <div className="mx-4 mb-10 flex flex-col items-center justify-center gap-8 text-center">
        <Typography variant="text-heading-sm">
          Adjusted Estimated SPP demand
        </Typography>
        <Typography className="mx-auto max-w-[600px]" variant="text-body-sm">
          {scenario === 'allGood' ? (
            <>
              Share price rises<span className="font-bold"> above </span>the
              issue price outlined in the SPP. Typically, these SPPs perform
              significantly better as it becomes cheaper to purchase shares
              through the SPP.
            </>
          ) : null}
          {scenario === 'rockyFinish' ? (
            <>
              Share price<span className="font-bold"> starts above </span>
              the issue price outlined in the SPP and
              <span className="font-bold"> trends downwards </span>during the
              SPP raise period. Share price rises the issue price outlined in
              the SPP. Typically, these SPPs perform significantly better as it
              becomes cheaper to purchase shares through the SPP.
            </>
          ) : null}
          {scenario === 'allBad' ? (
            <>
              Share price falls<span className="font-bold"> below </span>the
              issue price outlined in the SPP. Typically, these SPPs perform
              significantly worse as it becomes cheaper to purchase shares on
              the market.
            </>
          ) : null}
        </Typography>
        {loading ? (
          <div className="m-auto mt-1 h-3 w-20 animate-pulse bg-secondary-grey-light" />
        ) : null}
        <div className="m-auto flex w-2/3 flex-col items-center justify-center gap-6 xl:flex-row">
          <div className="flex min-w-max flex-col items-center">
            {loading ? (
              <div className="bg-amplify-green-700 h-3 w-16 animate-pulse" />
            ) : (
              <Typography variant="text-heading-lg">
                {numeral(
                  data?.currentCompanyStats?.raisingPotential.scenarioRange[
                    scenario
                  ].low
                ).format('$0.0a')}
              </Typography>
            )}
            <Typography variant="text-body-sm">Low</Typography>
          </div>
          <div className="hidden h-[1px] w-full bg-violet-800 xl:block" />
          <div>
            {loading ? (
              <div className="h-7 w-20 animate-pulse bg-secondary-grey-dark" />
            ) : (
              <Typography variant="text-heading-md">
                {numeral(
                  data?.currentCompanyStats?.raisingPotential.scenarioRange[
                    scenario
                  ].mean
                ).format('$0.0a')}
                *
              </Typography>
            )}
            <Typography variant="text-body-sm">Mid</Typography>
          </div>
          <div className="hidden h-[1px] w-full bg-violet-800 xl:block" />
          <div className="flex min-w-max flex-col items-center">
            {loading ? (
              <div className="h-3 w-16 animate-pulse bg-secondary-grey-dark" />
            ) : (
              <Typography variant="text-heading-md">
                {numeral(
                  data?.currentCompanyStats?.raisingPotential.scenarioRange[
                    scenario
                  ].high
                ).format('$0.0a')}
              </Typography>
            )}
            <Typography variant="text-body-sm">High</Typography>
          </div>
        </div>
        <Typography className="w-full" variant="text-caption">
          *Disclaimer: These estimates do not consist an offer to underwrite or
          purchase securities or to commit capital.
        </Typography>
      </div>
    </div>
  );
};

export const Scenarios = () => {
  const [scenario, setScenario] = useState<Scenario>('allGood');
  const { data } = useCurrentCompanyStatsQuery();

  const participationRateChange = useMemo(
    () =>
      data?.currentCompanyStats?.raisingPotential.scenarioRange[scenario]
        .shareholderParticipation && data?.currentCompanyStats?.raisingPotential
        ? data?.currentCompanyStats?.raisingPotential.scenarioRange[scenario]
            .shareholderParticipation -
          data?.currentCompanyStats?.raisingPotential?.shareholderParticipation
        : 0,
    [data?.currentCompanyStats?.raisingPotential, scenario]
  );

  const averageUptakeChange = useMemo(
    () =>
      data?.currentCompanyStats?.raisingPotential.scenarioRange[scenario]
        .averageUptake &&
      data?.currentCompanyStats?.raisingPotential.averageUptake
        ? data?.currentCompanyStats?.raisingPotential?.scenarioRange[scenario]
            .averageUptake -
          data?.currentCompanyStats?.raisingPotential.averageUptake
        : 0,

    [
      data?.currentCompanyStats?.raisingPotential.averageUptake,
      data?.currentCompanyStats?.raisingPotential.scenarioRange,
      scenario,
    ]
  );

  const Footer = useCallback(
    () => (
      <div>
        <div className="mx-4 grid grid-cols-1 gap-6 lg:mx-auto lg:max-w-[600px] lg:grid-cols-2">
          <div className="col-span-1 flex items-center gap-4 rounded-lg border bg-white p-4">
            <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-violet-50 text-violet-800">
              <UsersIcon className="h-5 w-5" />
            </div>
            <div className="text-left">
              <div className="flex items-center gap-1">
                <Typography className="text-gray-900" variant="text-heading-md">
                  {numeral(
                    data?.currentCompanyStats?.raisingPotential.scenarioRange[
                      scenario
                    ].shareholderParticipation
                  ).format('0%')}
                </Typography>

                {participationRateChange >= 0 ? (
                  <Badge LeadingIcon={() => <ArrowUpIcon />} color="green">
                    {Math.abs(Math.round(participationRateChange * 100))}%
                  </Badge>
                ) : (
                  <Badge LeadingIcon={() => <ArrowDownIcon />} color="red">
                    {Math.abs(Math.round(participationRateChange * 100))}%
                  </Badge>
                )}
              </div>
              <Typography className="text-gray-700" variant="text-body-sm">
                Participation rate compared to estimation
              </Typography>
            </div>
          </div>
          <div className="col-span-1 flex items-center gap-4 rounded-lg border bg-white p-4">
            <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-violet-50 text-violet-800">
              <UsersIcon className="h-5 w-5" />
            </div>
            <div className="text-left">
              <div className="flex items-center gap-1">
                <Typography className="text-gray-900" variant="text-heading-md">
                  {numeral(
                    data?.currentCompanyStats?.raisingPotential.scenarioRange[
                      scenario
                    ].averageUptake
                  ).format('$0,0')}
                </Typography>

                {averageUptakeChange >= 0 ? (
                  <Badge LeadingIcon={() => <ArrowUpIcon />} color="green">
                    {numeral(Math.abs(averageUptakeChange)).format('$0,0')}
                  </Badge>
                ) : (
                  <Badge LeadingIcon={() => <ArrowDownIcon />} color="red">
                    {numeral(Math.abs(averageUptakeChange)).format('$0,0')}
                  </Badge>
                )}
              </div>
              <Typography className="text-gray-700" variant="text-body-sm">
                Average uptake compared to estimation
              </Typography>
            </div>
          </div>
        </div>
      </div>
    ),
    [
      averageUptakeChange,
      data?.currentCompanyStats?.raisingPotential.scenarioRange,
      participationRateChange,
      scenario,
    ]
  );

  return (
    <div className="rounded-lg bg-violet-50 text-center text-violet-800">
      <ButtonGroup
        items={[
          {
            label: 'Strong performance',
            onClick: () => setScenario('allGood'),
            selected: scenario === 'allGood',
          },
          {
            label: 'Average performance',
            onClick: () => setScenario('rockyFinish'),
            selected: scenario === 'rockyFinish',
          },
          {
            label: 'Poor performance',
            onClick: () => setScenario('allBad'),
            selected: scenario === 'allBad',
          },
        ]}
      />
      <div className="flex flex-col py-12">
        <ScenarioInsights scenario={scenario} />
        <Footer />
      </div>
    </div>
  );
};

const SPPEstimationScenarios = () => {
  const { data, loading } = useCurrentCompanyStatsQuery();

  const numOfEmails = data?.currentCompanyStats?.shareholderInsights.email ?? 0;
  const totalShareholders =
    data?.currentCompanyStats?.shareholderInsights.total ?? 0;
  const emailsAvailability =
    numOfEmails > 0 && totalShareholders > 0
      ? numOfEmails / totalShareholders
      : 0;

  return (
    <div className="mx-6 my-8 grid grid-cols-1 gap-8 xl:grid-cols-4">
      <Typography
        className="col-span-full text-gray-500"
        variant="text-body-sm"
      >
        Key information about your shareholders and your engagement help us
        calculate your demand.
      </Typography>
      <div className="col-span-1 mr-auto h-full">
        <ShareholderInsight
          larger
          icon={<UserGroupIcon className="h-16 w-16" strokeWidth={1} />}
          loading={loading}
          title="Total shareholders"
          value={numeral(
            data?.currentCompanyStats?.shareholderInsights.total
          ).format('0,0')}
        />
      </div>
      <div className="col-span-1 gap-6 xl:col-span-3">
        <div className="mb-6 grid grid-cols-1 gap-6 xl:mb-0 xl:grid-cols-3">
          <ShareholderInsight
            icon={<PresentationChartLineIcon className="h-6 w-6" />}
            loading={loading}
            title="Shareholder growth ratio from past 90 days"
            value={numeral(
              data?.currentCompanyStats?.growthRatioPastNinetyDays
            ).format('0,0.00')}
          />
          <ShareholderInsight
            icon={<ScaleIcon className="h-6 w-6" />}
            loading={loading}
            title="Average holding time"
            value={formatAverageHoldingTime(
              data?.currentCompanyStats?.averageHoldingTime ?? 0
            )}
          />
          <ShareholderInsight
            icon={<FaceSmileIcon className="h-6 w-6" />}
            loading={loading}
            title="Shareholders in profit since initial investment"
            value={numeral(
              data?.currentCompanyStats?.profitLoss.shareholdersInProfit
            ).format('0,0%')}
          />
          <ShareholderInsight
            icon={<UsersIcon className="h-6 w-6" />}
            loading={loading}
            title="Estimated participation rate"
            value={numeral(
              data?.currentCompanyStats?.raisingPotential
                .shareholderParticipation
            ).format('0,0%')}
          />
          <ShareholderInsight
            icon={<CurrencyDollarIcon className="h-6 w-6" />}
            loading={loading}
            title="Estimated average uptake"
            value={numeral(
              data?.currentCompanyStats?.raisingPotential.averageUptake
            ).format('$0,0')}
          />
          <ShareholderInsight
            icon={<EnvelopeIcon className="h-6 w-6" />}
            loading={loading}
            title="Email available from shareholders"
            value={numeral(emailsAvailability).format('0,0%')}
          />
        </div>
      </div>
    </div>
  );
};

export default SPPEstimationScenarios;
