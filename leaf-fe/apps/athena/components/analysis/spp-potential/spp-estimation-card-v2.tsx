// DS V2
import React, { ReactElement } from 'react';
import analytics from '@analytics';
import { <PERSON>ton, Card, Typography, Tooltip } from '@ds';
import {
  UserIcon,
  UsersIcon,
  ArrowTrendingUpIcon,
  EnvelopeIcon,
  PhoneIcon,
} from '@heroicons/react-v2/24/outline';
import dayjs from 'dayjs';
import numeral from 'numeral';
import { useIntercom } from 'react-use-intercom';
import {
  useRaisesSppShareholderStatsQuery,
  useRaisesSppReachabilityStatsQuery,
  useSppEstimateQuery,
} from '@/apollo/generated';
import { formatAverageHoldingTime } from '@/utils/common-helpers';

interface StatProps {
  stat: ReactElement;
  statName: string;
  tooltipText?: string;
}

const Statistic: React.ComponentType<StatProps> = ({
  stat,
  statName,
  tooltipText,
}) => {
  return (
    <>
      <div className="w-full self-stretch border-b border-gray-200 pb-2 sm:flex-col md:inline-flex md:flex-row md:items-start md:justify-between">
        <div className="flex items-start justify-start gap-1">
          <Typography
            className="font-normal leading-tight text-gray-600"
            variant="text-body-sm"
          >
            {statName}
            {tooltipText && (
              <Tooltip
                description={tooltipText}
                iconClassName="text-gray-400"
              />
            )}
          </Typography>
        </div>
        <div className="flex items-start justify-start gap-1">
          <Typography
            className="font-normal leading-tight text-gray-600"
            variant="text-body-sm"
          >
            {stat}
          </Typography>
        </div>
      </div>
    </>
  );
};

const SPPEstimationCard: React.FC<{}> = () => {
  const sppShareholderStats = useRaisesSppShareholderStatsQuery();

  const sppReachStats = useRaisesSppReachabilityStatsQuery();

  const sppEstimate = useSppEstimateQuery();

  const { show } = useIntercom();

  return (
    <Card>
      {/* Estimation section */}
      <div className="inline-flex w-full flex-col items-start justify-center gap-8 rounded-lg border border-gray-200 bg-white p-6">
        <Typography
          className="text-lg font-semibold leading-7 text-gray-900"
          variant="text-heading-sm"
        >
          Understanding your predicted shareholder offer outcome
        </Typography>
        <div className="bg-amplify-green-50 flex flex-col items-center justify-start gap-4 self-stretch rounded-lg p-6">
          <div className="max-w-[600px] text-center">
            <Typography
              className="font-semibold leading-tight text-gray-900"
              variant="text-label-sm"
            >
              This is what our model predicts you would be able to raise at a
              greater than 50% likelihood from an SPP offer.
            </Typography>
          </div>
          <div className="flex flex-col items-center justify-start gap-2">
            <Typography
              className="text-center text-4xl font-semibold leading-[54px] text-gray-900"
              variant="text-heading-xl"
            >
              {sppEstimate?.loading
                ? 'Calculating...'
                : numeral(
                    sppEstimate?.data?.sppEstimate?.medEstimate.toPrecision(3)
                  ).format('$0,0')}
            </Typography>
            <Typography
              className="font-medium leading-none text-gray-500"
              variant="text-caption"
            >
              Last updated{' '}
              {sppEstimate?.loading
                ? '-'
                : dayjs(sppEstimate?.data?.sppEstimate?.updatedAt).format(
                    'DD MMM YYYY'
                  )}
            </Typography>
          </div>
        </div>
        <div>
          <div className="inline-flex w-full flex-col items-center justify-center gap-2 bg-white px-6">
            <Typography
              className="font-semibold leading-7 text-gray-900"
              variant="text-label-lg"
            >
              Improving your raise estimation.
            </Typography>
            <Typography className="self-stretch text-center text-base font-normal leading-normal text-gray-500">
              Your estimation is based on a combination of metrics like
              historical market data, your registry information and your
              shareholder reachability which you can improve to increase your
              estimated raise amount.
            </Typography>
          </div>
        </div>
        {/* Statistics */}
        <div className="w-full items-start justify-start sm:flex-col sm:gap-8 md:inline-flex md:flex-row md:gap-16 md:px-6">
          <div className="inline-flex w-full shrink grow basis-0 flex-col items-start justify-start md:gap-8">
            <div className="flex flex-col items-start justify-start gap-4 self-stretch py-4 md:gap-3 md:py-0">
              <div className="inline-flex items-start justify-start gap-2 self-stretch">
                <UserIcon className="relative h-5 w-5" />
                <Typography
                  className="font-semibold leading-tight text-gray-900"
                  variant="text-label-sm"
                >
                  Shareholder characteristics
                </Typography>
              </div>
              <Statistic
                stat={
                  <>
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? numeral(
                          sppShareholderStats.data.raisesSppShareholderStats
                            .totalShareholders
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="Total shareholders: "
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? numeral(
                          Math.round(
                            sppShareholderStats.data.raisesSppShareholderStats
                              .averageHoldSize
                          )
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="Average holding size: "
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? formatAverageHoldingTime(
                          sppShareholderStats.data.raisesSppShareholderStats
                            .averageHoldLength
                        )
                      : '-'}
                  </>
                }
                statName="Average holding length: "
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? numeral(
                          sppShareholderStats.data.raisesSppShareholderStats
                            .totalQual
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="Qualified investors: "
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? numeral(
                          sppShareholderStats.data.raisesSppShareholderStats
                            .totalHnws
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="High net worth trading behaviour:"
              ></Statistic>
            </div>
            <div className="flex flex-col items-start justify-start gap-4 self-stretch py-4 md:gap-3 md:py-0">
              <div className="inline-flex items-start justify-start gap-2 self-stretch">
                <ArrowTrendingUpIcon className="relative h-5 w-5" />
                <Typography
                  className="font-semibold leading-tight text-gray-900"
                  variant="text-label-sm"
                >
                  Shareholder trading behaviour
                </Typography>
              </div>
              <Statistic
                stat={
                  <>
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? numeral(
                          sppShareholderStats.data.raisesSppShareholderStats
                            .totalActive
                        ).format('0,0')
                      : '-'}{' '}
                    {'('}
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? (
                          (sppShareholderStats.data.raisesSppShareholderStats
                            .totalActive /
                            sppShareholderStats.data.raisesSppShareholderStats
                              .totalShareholders) *
                          100
                        ).toFixed(0)
                      : '-'}{' '}
                    {'% of shareholders)'}
                  </>
                }
                statName="Traded in the last 3 months:"
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ? numeral(
                          sppShareholderStats.data.raisesSppShareholderStats
                            .totalProfit
                        ).format('0,0')
                      : '-'}{' '}
                    {'('}
                    {sppShareholderStats.data?.raisesSppShareholderStats
                      ?.totalShareholders
                      ? (
                          (sppShareholderStats.data.raisesSppShareholderStats
                            .totalProfit /
                            sppShareholderStats.data.raisesSppShareholderStats
                              .totalShareholders) *
                          100
                        ).toFixed(0)
                      : '-'}{' '}
                    {'% of shareholders)'}
                  </>
                }
                statName="In profit since initial investment: "
              ></Statistic>
            </div>
          </div>
          <div className="inline-flex w-full shrink grow basis-0 flex-col items-start justify-start md:gap-8">
            <div className="flex flex-col items-start justify-start gap-4 self-stretch py-4 md:gap-3 md:py-0">
              <div className="inline-flex items-start justify-start gap-2 self-stretch">
                <UsersIcon className="relative h-5 w-5" />
                <Typography
                  className="font-semibold leading-tight text-gray-900"
                  variant="text-label-sm"
                >
                  Hub member data
                </Typography>
              </div>
              <Statistic
                stat={
                  <>
                    {sppReachStats.data?.raisesSppReachabilityStats
                      ? numeral(
                          sppReachStats.data.raisesSppReachabilityStats
                            .totalHubUsers
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="Total sign ups:"
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppReachStats.data?.raisesSppReachabilityStats
                      ? numeral(
                          sppReachStats.data.raisesSppReachabilityStats
                            .engagedHubUsers
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="Engaged visitors in the last 30 days: "
                tooltipText="Visitors that interacted with your hub via liking, following, asking questions and completing surveys."
              ></Statistic>
            </div>
            <div className="flex flex-col items-start justify-start gap-4 self-stretch py-4 md:gap-3 md:py-0">
              <div className="inline-flex items-start justify-start gap-2 self-stretch">
                <PhoneIcon className="relative h-5 w-5" />
                <Typography
                  className="font-semibold leading-tight text-gray-900"
                  variant="text-label-sm"
                >
                  Registry reachability
                </Typography>
              </div>
              <Statistic
                stat={
                  <>
                    {sppReachStats.data?.raisesSppReachabilityStats
                      ? numeral(
                          sppReachStats.data.raisesSppReachabilityStats
                            .totalEmails
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="Email addresses available: "
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppReachStats.data?.raisesSppReachabilityStats
                      ? numeral(
                          sppReachStats.data.raisesSppReachabilityStats
                            .totalPhones
                        ).format('0,0')
                      : '-'}
                  </>
                }
                statName="Phone numbers available:"
              ></Statistic>
            </div>
            <div className="flex flex-col items-start justify-start gap-4 self-stretch py-4 md:gap-3 md:py-0">
              <div className="inline-flex items-start justify-start gap-2 self-stretch">
                <EnvelopeIcon className="relative h-5 w-5" />
                <Typography
                  className="font-semibold leading-tight text-gray-900"
                  variant="text-label-sm"
                >
                  Campaign performance
                </Typography>
              </div>
              <Statistic
                stat={
                  <>
                    {sppReachStats.data?.raisesSppReachabilityStats
                      ? (
                          sppReachStats.data.raisesSppReachabilityStats
                            .openRate * 100
                        ).toFixed(0)
                      : '-'}{' '}
                    {' %'}
                  </>
                }
                statName="Open rate of campaigns in the last 30 days:"
              ></Statistic>
              <Statistic
                stat={
                  <>
                    {sppReachStats.data?.raisesSppReachabilityStats
                      ? (
                          sppReachStats.data.raisesSppReachabilityStats
                            .clickRate * 100
                        ).toFixed(0)
                      : '-'}{' '}
                    {' %'}
                  </>
                }
                statName="Click rate of campaigns in the last 30 days:"
              ></Statistic>
            </div>
          </div>
        </div>
        {/* Raise button */}
        <div className="bg-amplify-green-50 inline-flex h-32 w-full flex-col items-center justify-center gap-4 rounded-lg p-6">
          <Typography
            className="text-center font-semibold leading-tight text-gray-900"
            variant="text-label-sm"
          >
            Looking to plan your SPP or increase your raising potential?
          </Typography>
          <Button
            onClick={() => {
              show();
              analytics.track('spp_speak_to_a_raising_specialist');
            }}
          >
            Speak to a raising specialist
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default SPPEstimationCard;
