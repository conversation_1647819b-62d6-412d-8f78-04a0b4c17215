// DS V2
import React from 'react';
import { Typo<PERSON>, Button } from '@ds';
import { useRouter } from 'next/router';
import { useCurrentCompanyProfileUser } from '@/contexts/current-company-profile-user-context';
import routes from '@/utils/routes';

const RegistryNotConnectedCTA = () => {
  const { isComputershare } = useCurrentCompanyProfileUser();

  const router = useRouter();

  const goToRegistrySettings = () =>
    router.push(
      routes.settings.registry.href(router.query.marketListingKey as string)
    );

  const message = isComputershare
    ? `Integration with Computershare is not supported. To use this feature, please contact your Client Success Manager.`
    : `To use this feature, please connect your registry.`;

  return (
    <div className="p-4">
      <div className="flex max-w-sm flex-col items-center justify-center space-y-8 rounded-lg bg-white px-8 py-6 shadow-md">
        <Typography className="text-center">{message}</Typography>
        {!isComputershare && (
          <Button onClick={goToRegistrySettings}>
            Go to registry settings
          </Button>
        )}
      </div>
    </div>
  );
};

export default RegistryNotConnectedCTA;
