// DS V2
import React from 'react';
import { Typography } from '@ds';
import capitalize from 'lodash/capitalize';
import { useRegistryImportStatusQuery } from '@/apollo/generated';

const ImportingDataCTA = () => {
  const { data } = useRegistryImportStatusQuery();

  const message = `This feature will be unlocked after data integration with ${capitalize(
    data?.registryImportStatus?.registry
  )} has completed.`;

  return (
    <div className="p-4">
      <div className="flex flex-col items-center justify-center gap-6 rounded-lg bg-white p-6 shadow-md">
        <Typography className="text-center">{message}</Typography>
      </div>
    </div>
  );
};

export default ImportingDataCTA;
