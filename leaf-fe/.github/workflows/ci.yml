name: CI
on:
  push:
    branches:
      - master
  pull_request:

jobs:
  master:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    if: ${{ github.event_name != 'pull_request' }}
    steps:
      - uses: actions/checkout@v4
        name: Checkout [master]
        with:
          fetch-depth: 0
      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: 'master'
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
      - uses: useblacksmith/setup-node@v5
        with:
          node-version-file: '.tool-versions'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm unused-exports
      - run: pnpm prettier --check '**/*.(css|graphql|json|yml)'
      - run: npx nx affected --target=lint --maxWarnings=0 --parallel=8
      - run: npx nx affected --target=tsc --parallel=8
      - run: pnpm test:run
  pr:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    if: ${{ github.event_name == 'pull_request' }}
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          fetch-depth: 0
      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: 'master'
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
      - uses: useblacksmith/setup-node@v5
        with:
          node-version-file: '.tool-versions'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm unused-exports
      - run: pnpm prettier --check '**/*.(css|graphql|json|yml)'
      - name: Run ESLint on your changes only
        env:
          ESLINT_PLUGIN_DIFF_COMMIT: ${{ github.event.pull_request.base.ref }}
        run: npx nx affected --target=lint --maxWarnings=0 --parallel=8
      - run: npx nx affected --target=tsc --parallel=8
      - run: pnpm test:run
