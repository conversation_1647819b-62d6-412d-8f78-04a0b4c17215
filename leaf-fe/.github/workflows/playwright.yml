name: Playwright Tests
on: workflow_dispatch
jobs:
  test:
    timeout-minutes: 60
    runs-on: blacksmith-4vcpu-ubuntu-2204
    env:
      # PLY is the ticker of our staging Playwright organisation
      ATHENA_TEST_API_URL: https://athena-staging.investorhub.com
      ATHENA_TEST_URL: https://app-staging.investorhub.com/ply
      HERMES_TEST_URL: https://ply.fresh-staging.xyz
      HERMES_LOCK_SCREEN_PWD: ${{ secrets.HERMES_LOCK_SCREEN_PWD }}
      MAILOSAUR_API_KEY: ${{ secrets.MAILOSAUR_API_KEY }}
      MAILOSAUR_EMAIL_HOSTNAME: ${{ secrets.MAILOSAUR_EMAIL_HOSTNAME }}
      MAILOSAUR_SERVER_ID: ${{ secrets.MAILOSAUR_SERVER_ID }}
      TEST_USER_PASSWORD: ${{ secrets.TEST_USER_PASSWORD }}
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v2
        with:
          version: 8
      - uses: useblacksmith/setup-node@v5
        with:
          node-version: 18
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - name: Install Playwright Browsers
        run: pnpm playwright install --with-deps
      - name: Run sign up Playwright tests
        run: pnpm run-signup-e2e
      - name: Run remaining Playwright tests
        run: pnpm run-e2e
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
