---
description:
globs: *.tsx
alwaysApply: false
---
The frontend is a NX monorepo. The two main apps are athena and hermes, which are NextJS projects.

- We use pnpm for our package manager.
- always dasherize filenames "here-is-some-file.tsx"
- don't import from relative paths. We have a shortcut path `@` which refers to the lib directory. For Athena it's `leaf-fe/apps/athena/lib` and for Her<PERSON> it's `leaf-fe/apps/hermes/lib`. For example, `import { useMedia } from '@/components/content/shared/contexts/media-context';`
- when importing heroicons, instead of `@heroicons/react/24/solid`, use `@heroicons/react-v2/24/solid`. Ensure `react-v2` is in there
- when wanting to display toast alerts in athena, always use leaf-fe/apps/athena/contexts/alert-context.tsx. Import it from `@ds`.
- we use apollo and the package `@graphql-codegen/typescript-react-apollo`.
- when asked to create qraphql, don't do it inline, but create a new graphql file in the appropriate directory based on the app:
athena: `leaf-fe/apps/athena/apollo/operations/mutations` & `leaf-fe/apps/athena/apollo/operations/queries`
hermes: `leaf-fe/apps/hermes/apollo/operations/mutations` & `leaf-fe/apps/hermes/apollo/operations/queries`
- Make sure you use dasherized filenames like `get-media.graphql`.
After adding or modifying these files, we need to run the command to regenerate the types:
athena: `cd leaf-fe && pnpm nx run athena:codegen`
hermes: `cd leaf-fe && pnpm nx run hermes:codegen`

For example, say you are asked to fetch the media item and want to create a graphql endpoint in athena.
New file: `leaf-fe/apps/athena/apollo/operations/queries/get-media.graphql`.
```
query GetMedia($id: ID!) {
  media(id: $id) {
    id
  }
}
```
Always use camel case in graphql files. On the server they will be snake case automatically.
Then run `pnpm nx run athena:codegen` in the terminal.
Then in a file to use it:
```
import { useGetMediaQuery } from '@/apollo/generated';

const ContentMediaPage: React.ComponentType = () => {
  const router = useRouter();
  const { mediaId } = router.query;

  const { data, loading } = useGetMediaQuery({
    skip: !mediaId,
    variables: {
      id: mediaId as string,
    },
  });

  return (
    <MediaProvider media={data?.media}>
      <MediaLayout>
        <ContentMedia />
      </MediaLayout>
    </MediaProvider>
  );
};
```

So if we pull data like this:

```
import { useMediasQuery } from '@/apollo/generated';
const { data, loading } = useMediasQuery({variables: searchVariables});
```

Then any time we want to reference the type for an object returned from the query we do it like this:

```
import { MediasQuery } from '@/apollo/generated';

// Type for all data:
interface SomeProps {
  data?: MediasQuery;
}

// Type for medias:
interface SomeProps {
  medias: NonNullable<NonNullable<MediasQuery['medias']>['edges']>;
}

// Type for a single media:
interface SomeProps {
  media: NonNullable<
    NonNullable<NonNullable<MediasQuery['medias']>['edges']>[0]
  >['node'];
}

// Type for a property on the media object:
interface SomeProps {
  mediaUpdate: NonNullable<
    NonNullable<
      NonNullable<MediasQuery['medias']>['edges']
    >[0]
  >['node']['mediaUpdate'];
}
```


When refetching data, always use refetchQueries. eg:

```
const [deleteStaticList, { loading: mutationLoading }] =
    useDeleteStaticListMutation({
      awaitRefetchQueries: true,
      refetchQueries: ['StaticLists'],
      variables: { id: node.id },
    });
```
Don't reload the page.
